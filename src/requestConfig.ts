import {BACKEND_HOST_LOCAL, BACKEND_HOST_PROD} from '@/constants';
import type {RequestOptions} from '@@/plugin-request/request';
//运行时配置
import type {RequestConfig} from '@umijs/max';

// 与后端约定的响应数据格式
interface ResponseStructure {
  success: boolean;
  data: any;
  errorCode?: number;
  errorMessage?: string;
}

//Node.js 环境中的一个全局变量，用于标识当前的运行环境模式
const isDev = process.env.NODE_ENV === 'development';

/**
 * @name 错误处理
 * pro 自带的错误处理， 可以在这里做自己的改动
 * @doc https://umijs.org/docs/max/request#配置
 */
export const requestConfig: RequestConfig = {
  baseURL: isDev ? BACKEND_HOST_LOCAL : BACKEND_HOST_PROD,
  //允许携带cookie等...
  withCredentials: true,

  // 请求拦截器
  requestInterceptors: [
    //config:这是请求拦截器函数接收的参数，代表即将发送的 HTTP 请求的配置对象
    (config: RequestOptions) => {
      return config;
    },
  ],

  // 响应拦截器
  responseInterceptors: [
    //response:这是响应拦截器函数接收的参数，代表响应对象
    (response) => {
    console.log('response', response);
      // 请求地址(提取URL路径)
      const requestPath: string = response.config.url ?? '';
      // 响应
      const {data} = response as unknown as ResponseStructure;
      if (!data) {
        throw new Error('服务异常');
      }

      // 文件下载时，直接返回
      if (data instanceof Blob) {
        return response;
      }

      // 错误码处理
      const code: number = data.code;
      // 状态为登录,请求路径不包含user/get/login(获取当前登录用户),当前浏览器页面路径是否不包含 /user/login
      if (code === 40100 && !location.pathname.includes('/user/login')) {
        // 跳转至登录页
        // window.location.href加密
        const md5 = encodeURIComponent(window.location.href);
        window.location.href = `/user/login?redirect=${md5}`;
        throw new Error('请先登录');
      }

      //这里0是业务接口响应正常
      if (code !== 0) {
        throw new Error(data.description && data.description.trim() ? data.description : data.message ?? '服务器错误');
      }
      return response;
    },
  ],
};
