declare namespace generatorBackend {
  type DeleteRequest = {
    id?: number;
  };

  type FileConfigInfo = {
    files?: FileInfo[];
    inputRootPath?: string;
    outputRootPath?: string;
    sourceRootPath?: string;
    type?: string;
  };

  type FileInfo = {
    condition?: string;
    files?: FileInfo[];
    generateType?: string;
    groupKey?: string;
    groupName?: string;
    inputPath?: string;
    outputPath?: string;
    type?: string;
  };

  type Generator = {
    author?: string;
    basePackage?: string;
    createTime?: string;
    description?: string;
    distPath?: string;
    fileConfig?: string;
    id?: number;
    isDelete?: number;
    modelConfig?: string;
    name?: string;
    picture?: string;
    status?: number;
    tags?: string;
    updateTime?: string;
    userId?: number;
    version?: string;
  };

  type GeneratorAddRequest = {
    author?: string;
    basePackage?: string;
    description?: string;
    distPath?: string;
    fileConfig?: FileConfigInfo;
    modelConfig?: ModelConfigInfo;
    name?: string;
    picture?: string;
    status?: number;
    tags?: string[];
    version?: string;
  };

  type GeneratorQueryRequest = {
    author?: string;
    basePackage?: string;
    content?: string;
    current?: number;
    description?: string;
    distPath?: string;
    id?: number;
    name?: string;
    notId?: number;
    orTags?: string[];
    pageSize?: number;
    searchText?: string;
    sortField?: string;
    sortOrder?: string;
    status?: number;
    tags?: string[];
    title?: string;
    userId?: number;
    version?: string;
  };

  type GeneratorUpdateRequest = {
    author?: string;
    basePackage?: string;
    description?: string;
    distPath?: string;
    fileConfig?: FileConfigInfo;
    id?: number;
    modelConfig?: ModelConfigInfo;
    name?: string;
    picture?: string;
    status?: number;
    tags?: string[];
    version?: string;
  };

  type GeneratorVO = {
    author?: string;
    basePackage?: string;
    createTime?: string;
    description?: string;
    distPath?: string;
    fileConfig?: FileConfigInfo;
    id?: number;
    modelConfig?: ModelConfigInfo;
    name?: string;
    picture?: string;
    status?: number;
    tags?: string[];
    updateTime?: string;
    user?: UserVO;
    userId?: number;
    version?: string;
  };

  type getGeneratorVOByIdUsingGETParams = {
    /** id */
    id?: number;
  };

  type LoginUserVO = {
    createTime?: string;
    id?: number;
    updateTime?: string;
    userAvatar?: string;
    userName?: string;
    userProfile?: string;
    userRole?: string;
  };

  type ModelConfigInfo = {
    models?: ModelInfo[];
  };

  type ModelInfo = {
    abbr?: string;
    allArgsStr?: string;
    condition?: string;
    defaultValue?: Record<string, any>;
    description?: string;
    fieldName?: string;
    groupKey?: string;
    groupName?: string;
    models?: ModelInfo[];
    type?: string;
  };

  type PageGenerator_ = {
    current?: number;
    pages?: number;
    records?: Generator[];
    size?: number;
    total?: number;
  };

  type PageGeneratorVO_ = {
    current?: number;
    pages?: number;
    records?: GeneratorVO[];
    size?: number;
    total?: number;
  };

  type ResultBoolean_ = {
    code?: number;
    data?: boolean;
    description?: string;
    message?: string;
  };

  type ResultGeneratorVO_ = {
    code?: number;
    data?: GeneratorVO;
    description?: string;
    message?: string;
  };

  type ResultLoginUserVO_ = {
    code?: number;
    data?: LoginUserVO;
    description?: string;
    message?: string;
  };

  type ResultLong_ = {
    code?: number;
    data?: number;
    description?: string;
    message?: string;
  };

  type ResultPageGenerator_ = {
    code?: number;
    data?: PageGenerator_;
    description?: string;
    message?: string;
  };

  type ResultPageGeneratorVO_ = {
    code?: number;
    data?: PageGeneratorVO_;
    description?: string;
    message?: string;
  };

  type ResultString_ = {
    code?: number;
    data?: string;
    description?: string;
    message?: string;
  };

  type UserLoginRequest = {
    userAccount?: string;
    userPassword?: string;
  };

  type UserRegisterRequest = {
    checkPassword?: string;
    userAccount?: string;
    userPassword?: string;
  };

  type UserVO = {
    createTime?: string;
    id?: number;
    userAvatar?: string;
    userName?: string;
    userProfile?: string;
    userRole?: string;
  };
}
