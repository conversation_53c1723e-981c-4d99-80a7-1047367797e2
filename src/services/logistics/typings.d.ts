declare namespace API {
  type DeleteRequest = {
    id?: number;
  };

  type DriverVO = {
    account?: string;
    id?: number;
    mobile?: string;
    name?: string;
    orgId?: number;
    orgName?: string;
    orgTreeIds?: string[];
    status?: number;
  };

  type editUsingPOSTParams = {
    allowableType?: string;
    driverAge?: number;
    initialCertificateDate?: string;
    licenseNumber?: string;
    licenseType?: string;
    passCertificate?: string;
    pictures?: string[];
    qualificationCertificate?: string;
    userId?: number;
    validPeriod?: string;
  };

  type getByIdUsingGETParams = {
    /** id */
    id: number;
  };

  type getByTruckIdUsingGETParams = {
    /** id */
    id: number;
  };

  type getByUserIdUsingGETParams = {
    /** id */
    id: number;
  };

  type PageDriverVO_ = {
    current?: number;
    pages?: number;
    records?: DriverVO[];
    size?: number;
    total?: number;
  };

  type PageTruckType_ = {
    current?: number;
    pages?: number;
    records?: TruckType[];
    size?: number;
    total?: number;
  };

  type PageTruckVO_ = {
    current?: number;
    pages?: number;
    records?: TruckVO[];
    size?: number;
    total?: number;
  };

  type ResultBoolean_ = {
    code?: number;
    data?: boolean;
    description?: string;
    message?: string;
  };

  type ResultListTruckTypeVO_ = {
    code?: number;
    data?: TruckTypeVO[];
    description?: string;
    message?: string;
  };

  type ResultLong_ = {
    code?: number;
    data?: number;
    description?: string;
    message?: string;
  };

  type ResultPageDriverVO_ = {
    code?: number;
    data?: PageDriverVO_;
    description?: string;
    message?: string;
  };

  type ResultPageTruckType_ = {
    code?: number;
    data?: PageTruckType_;
    description?: string;
    message?: string;
  };

  type ResultPageTruckVO_ = {
    code?: number;
    data?: PageTruckVO_;
    description?: string;
    message?: string;
  };

  type ResultString_ = {
    code?: number;
    data?: string;
    description?: string;
    message?: string;
  };

  type ResultTruckDriverLicenseEditRequest_ = {
    code?: number;
    data?: TruckDriverLicenseEditRequest;
    description?: string;
    message?: string;
  };

  type ResultTruckLicenseEditRequest_ = {
    code?: number;
    data?: TruckLicenseEditRequest;
    description?: string;
    message?: string;
  };

  type ResultTruckVO_ = {
    code?: number;
    data?: TruckVO;
    description?: string;
    message?: string;
  };

  type TruckAddRequest = {
    deviceGpsId?: string;
    licensePlate?: string;
    status?: number;
    truckTypeId?: number;
    workStatus?: number;
  };

  type TruckDriverLicenseEditRequest = {
    allowableType?: string;
    driverAge?: number;
    initialCertificateDate?: string;
    licenseNumber?: string;
    licenseType?: string;
    passCertificate?: string;
    pictures?: string[];
    qualificationCertificate?: string;
    userId?: number;
    validPeriod?: string;
  };

  type TruckEditRequest = {
    allowableLoad?: number;
    allowableVolume?: number;
    deviceGpsId?: string;
    id?: number;
    licensePlate?: string;
    pictures?: string[];
    truckTypeId?: number;
  };

  type TruckLicenseEditRequest = {
    allowableWeight?: number;
    engineNumber?: string;
    expirationDate?: string;
    mandatoryScrap?: string;
    overallQuality?: number;
    pictures?: string[];
    registrationDate?: string;
    transportCertificateNumber?: string;
    truckId?: number;
    validityPeriod?: string;
  };

  type TruckQueryRequest = {
    current?: number;
    licensePlate?: string;
    pageSize?: number;
    sortField?: string;
    sortOrder?: string;
    truckTypeId?: number;
  };

  type TruckType = {
    allowableLoad?: number;
    allowableVolume?: number;
    created?: string;
    id?: number;
    measureHigh?: number;
    measureLong?: number;
    measureWidth?: number;
    name?: string;
    status?: number;
    updated?: string;
  };

  type TruckTypeAddRequest = {
    allowableLoad?: number;
    allowableVolume?: number;
    measureHigh?: number;
    measureLong?: number;
    measureWidth?: number;
    name?: string;
    status?: number;
  };

  type TruckTypeQueryRequest = {
    allowableLoad?: number;
    allowableVolume?: number;
    current?: number;
    name?: string;
    pageSize?: number;
    sortField?: string;
    sortOrder?: string;
    status?: number;
  };

  type TruckTypeUpdateRequest = {
    allowableLoad?: number;
    allowableVolume?: number;
    created?: string;
    id?: number;
    measureHigh?: number;
    measureLong?: number;
    measureWidth?: number;
    name?: string;
    status?: number;
    updated?: string;
  };

  type TruckTypeVO = {
    id?: number;
    name?: string;
  };

  type TruckUpdateStatusRequest = {
    id?: number;
    status?: number;
  };

  type TruckVO = {
    allowableLoad?: number;
    allowableVolume?: number;
    deviceGpsId?: string;
    id?: number;
    licensePlate?: string;
    pictures?: string[];
    status?: number;
    truckTypeId?: number;
    workStatus?: number;
  };

  type UserQueryRequest = {
    current?: number;
    mobile?: string;
    name?: string;
    orgId?: number;
    pageSize?: number;
    roleType?: string;
    sortField?: string;
    sortOrder?: string;
    stationId?: number;
    status?: number;
  };
}
