// @ts-ignore
/* eslint-disable */
import { request } from "@umijs/max";

/** edit POST /api/truckDriverLicense/edit */
export async function editUsingPost(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.editUsingPOSTParams,
  options?: { [key: string]: any }
) {
  return request<API.ResultLong_>("/api/truckDriverLicense/edit", {
    method: "POST",
    params: {
      ...params,
    },
    ...(options || {}),
  });
}
