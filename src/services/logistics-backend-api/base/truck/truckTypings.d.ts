declare namespace TRUCK_API {
  type TruckAddRequest = {
    deviceGpsId?: string;
    licensePlate?: string;
    status?: number;
    truckTypeId?: number;
    workStatus?: number;
  };

  type TruckEditRequest = {
    allowableLoad?: number;
    allowableVolume?: number;
    deviceGpsId?: string;
    id?: number;
    licensePlate?: string;
    pictures?: string[];
    truckTypeId?: number;
  };

  type TruckQueryRequest = {
    current?: number;
    licensePlate?: string;
    pageSize?: number;
    sortField?: string;
    sortOrder?: string;
    truckTypeId?: number;
  };

  type ResultPageTruckListVO_ = {
    code?: number;
    data?: PageTruckListVO_;
    description?: string;
    message?: string;
  };
  type PageTruckListVO_ = {
    current?: number;
    pages?: number;
    records?: TruckVO[];
    size?: number;
    total?: number;
  };


  type TruckVO = {
    allowableLoad?: number;
    allowableVolume?: number;
    deviceGpsId?: string;
    id?: number;
    licensePlate?: string;
    truckTypeId?: number;
    workStatus?: number;
    status?: number;
  };

  type TruckUpdateStatusRequest = {
    id?: number;
    status?: number;
  };

  type getByIdUsingGETParams = {
    /** id */
    id: number;
  };

  type ResultTruckVO_ = {
    code?: number;
    data?: TruckVO;
    description?: string;
    message?: string;
  };
}

