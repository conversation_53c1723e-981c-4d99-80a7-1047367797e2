// @ts-ignore
/* eslint-disable */
import { request } from "@umijs/max";


/** listQueryTruckDriver POST /api/truckDriver/list */
export async function listQueryTruckDriverUsingPost(
  body: USER_API.UserQueryRequest,
  options?: { [key: string]: any }
) {
  return request<TRUCK_DRIVER_API.ResultPageDriverVO_>("/base/truckDriver/list", {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    data: body,
    ...(options || {}),
  });
}
