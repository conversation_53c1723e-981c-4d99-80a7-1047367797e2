declare namespace TRUCK_DRIVER_API {
  type ResultPageDriverVO_ = {
    code?: number;
    data?: PageDriverVO_;
    description?: string;
    message?: string;
  };
  type PageDriverVO_ = {
    current?: number;
    pages?: number;
    records?: DriverVO[];
    size?: number;
    total?: number;
  };

  type DriverVO = {
    account?: string;
    id?: number;
    mobile?: string;
    name?: string;
    orgId?: number;
    orgName?: string;
    orgTreeIds?: string[];
    status?: number;
  };

}

