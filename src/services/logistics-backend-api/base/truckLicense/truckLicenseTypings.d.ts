declare namespace TRUCK_LICENSE_API {
  type editUsingPOST1Params = {
    allowableWeight?: number; //核定载质量
    engineNumber?: string; //发动机编号
    expirationDate?: string; //检验有效期
    id?: number;
    mandatoryScrap?: string; //国强强制报废时间
    overallQuality?: number; //整备质量
    pictures?: string[];
    registrationDate?: string; //注册时间
    transportCertificateNumber?: string; //行驶证号
    validityPeriod?: string; //行驶证有效期
  };

  type getByTruckIdUsingGETParams = {
    /** id */
    id: string;
  };

  type ResultTruckLicenseEditRequest_ = {
    code?: number;
    data?: TruckLicenseEditRequest;
    description?: string;
    message?: string;
  };
  type TruckLicenseEditRequest = {
    allowableWeight?: number;
    engineNumber?: string;
    expirationDate?: string;
    id?: number;
    mandatoryScrap?: string;
    overallQuality?: number;
    pictures?: string[];
    registrationDate?: string;
    transportCertificateNumber?: string;
    validityPeriod?: string;
  };

}

