// @ts-ignore
/* eslint-disable */
import { request } from "@umijs/max";

/** edit POST /api/truckLicense/edit */
export async function editUsingPost(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: TRUCK_LICENSE_API.editUsingPOST1Params,
  options?: { [key: string]: any }
) {
  return request<API.ResultLong_>("/base/truckLicense/edit", {
    method: "POST",
    params: {
      ...params,
    },
    ...(options || {}),
  });
}




/** getByTruckId GET /api/truckLicense/getByTruckId/${param0} */
export async function getByTruckIdUsingGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.getByTruckIdUsingGETParams,
  options?: { [key: string]: any }
) {
  const { id: param0, ...queryParams } = params;
  return request<API.ResultTruckLicenseEditRequest_>(
    `/api/truckLicense/getByTruckId/${param0}`,
    {
      method: "GET",
      params: { ...queryParams },
      ...(options || {}),
    }
  );
}
