declare namespace TRUCK_DRIVER_LICENSE_API {
  type editUsingPOSTParams = {
    allowableType?: string; //准驾车型
    driverAge?: number; //驾龄
    initialCertificateDate?: string; //初次领证日期
    licenseNumber?: string; //驾驶证号
    licenseType?: string; //驾驶证类型
    passCertificate?: string; //入场证信息 (可为NULL)
    pictures?: string[];
    qualificationCertificate?: string; //从业资格证信息 (可为NULL)
    userId?: number;
    validPeriod?: string; //有效周期
  };
  type getByUserIdUsingGETParams = {
    /** id */
    id: number;
  };

  type ResultTruckDriverLicenseEditRequest_ = {
    code?: number;
    data?: editUsingPOSTParams;
    description?: string;
    message?: string;
  };
}

