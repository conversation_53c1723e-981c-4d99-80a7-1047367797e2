// @ts-ignore
/* eslint-disable */
import { request } from "@umijs/max";

/** edit POST /api/truckDriverLicense/edit */
export async function editUsingPost(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: TRUCK_DRIVER_LICENSE_API.editUsingPOSTParams,
  options?: { [key: string]: any }
) {
  return request<API.ResultLong_>("/base/truckDriverLicense/edit", {
    method: "POST",
    params: {
      ...params,
    },
    ...(options || {}),
  });
}


/** getByUserId GET /api/truckDriverLicense/getByUserId/${param0} */
export async function getByUserIdUsingGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: TRUCK_DRIVER_LICENSE_API.getByUserIdUsingGETParams,
  options?: { [key: string]: any }
) {
  const { id: param0, ...queryParams } = params;
  return request<TRUCK_DRIVER_LICENSE_API.ResultTruckDriverLicenseEditRequest_>(
    `/base/truckDriverLicense/getByUserId/${param0}`,
    {
      method: "GET",
      params: { ...queryParams },
      ...(options || {}),
    }
  );
}
