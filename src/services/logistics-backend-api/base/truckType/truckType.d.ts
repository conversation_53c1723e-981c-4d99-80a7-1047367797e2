declare namespace TRUCK_TYPE_API {
  type TruckTypeQueryRequest = {
    allowableLoad?: number;
    allowableVolume?: number;
    current?: number;
    name?: string;
    pageSize?: number;
    sortField?: string;
    sortOrder?: string;
    status?: number;
  };

  type ResultPageTruckType_ = {
    code?: number;
    data?: PageTruckType_;
    description?: string;
    message?: string;
  };

  type PageTruckType_ = {
    current?: number;
    pages?: number;
    records?: TruckType[];
    size?: number;
    total?: number;
  };

  type TruckType = {
    allowableLoad?: number;
    allowableVolume?: number;
    created?: string;
    id?: number;
    measureHigh?: number;
    measureLong?: number;
    measureWidth?: number;
    name?: string;
    status?: number;
    updated?: string;
  };

  type TruckTypeUpdateRequest = {
    allowableLoad?: number;
    allowableVolume?: number;
    created?: string;
    id?: number;
    measureHigh?: number;
    measureLong?: number;
    measureWidth?: number;
    name?: string;
    status?: number;
    updated?: string;
  };
  type TruckTypeAddRequest = {
    allowableLoad?: number;
    allowableVolume?: number;
    measureHigh?: number;
    measureLong?: number;
    measureWidth?: number;
    name?: string;
    status?: number;
  };
  type ResultListTruckTypeVO_ = {
    code?: number;
    data?: TruckTypeVO[];
    description?: string;
    message?: string;
  };

  type TruckTypeVO = {
    id?: number;
    name?: string;
  };
}
