// @ts-ignore
/* eslint-disable */
import { request } from "@umijs/max";

/** list POST /api/truckType/list */
export async function listUsingPost(
  body: TRUCK_TYPE_API.TruckTypeQueryRequest,
  options?: { [key: string]: any }
) {
  return request<TRUCK_TYPE_API.ResultPageTruckType_>("/base/truckType/list", {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    data: body,
    ...(options || {}),
  });
}
/** update POST /api/truckType/update */
export async function updateUsingPost(
  body: TRUCK_TYPE_API.TruckTypeUpdateRequest,
  options?: { [key: string]: any }
) {
  return request<API.ResultBoolean_>("/base/truckType/update", {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    data: body,
    ...(options || {}),
  });
}


/** add POST /api/truckType/add */
export async function addUsingPost(
  body: TRUCK_TYPE_API.TruckTypeAddRequest,
  options?: { [key: string]: any }
) {
  return request<API.ResultLong_>("/base/truckType/add", {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    data: body,
    ...(options || {}),
  });
}


/** delete DELETE /api/truckType/delete */
export async function deleteUsingDelete(
  body: API.DeleteRequest,
  options?: { [key: string]: any }
) {
  return request<API.ResultString_>("/base/truckType/delete", {
    method: "DELETE",
    headers: {
      "Content-Type": "application/json",
    },
    data: body,
    ...(options || {}),
  });
}


/** getAllVO GET /api/truckType/getAll */
export async function getAllVoUsingGet(options?: { [key: string]: any }) {
  return request<TRUCK_TYPE_API.ResultListTruckTypeVO_>("/base/truckType/getAll", {
    method: "GET",
    ...(options || {}),
  });
}
