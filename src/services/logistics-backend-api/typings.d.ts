declare namespace API {
  type CoreStationAddRequest = {
    description?: string;
    name?: string;
    orgId?: number;
    status?: number;
  };

  type LoginUserVO = {
    avatar?: string;
    id?: number;
    name?: string;
    sex?: string;
    workDescribe?: string;
  };

  type ResultLoginUserVO_ = {
    code?: number;
    data?: LoginUserVO;
    description?: string;
    message?: string;
  };

  type ResultLong_ = {
    code?: number;
    data?: number;
    description?: string;
    message?: string;
  };

  type ResultString_ = {
    code?: number;
    data?: string;
    description?: string;
    message?: string;
  };

  type RoleAddRequest = {
    description?: string;
    dsType?: string;
    name?: string;
    repel?: number;
    status?: number;
  };

  type UserAddRequest = {
    account?: string;
    avatar?: string;
    email?: string;
    mobile?: string;
    name?: string;
    orgId?: number;
    sex?: string;
    stationId?: number;
    status?: number;
    superior?: number;
    workDescribe?: string;
  };

  type UserLoginRequest = {
    account?: string;
    password?: string;
  };

  type UserRoleAddRequest = {
    roleId?: number;
    userId?: number;
  };
}
