// @ts-ignore
/* eslint-disable */
import { request } from "@umijs/max";

/** addCoreStation POST /api/coreOrg/add */
export async function addCoreStationUsingPost(
  body: CORE_ORG_API.CoreOrgAddRequest,
  options?: { [key: string]: any }
) {
  return request<API.ResultLong_>("/author/coreOrg/add", {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    data: body,
    ...(options || {}),
  });
}

/** listAllCoreOrgTree POST /api/coreOrg/list/tree */
export async function listAllCoreOrgTreeUsingPost(options?: {
  [key: string]: any;
}) {
  return request<CORE_ORG_API.ResultListCoreOrgTreeVo_>("/author/coreOrg/list/tree", {
    method: "POST",
    ...(options || {}),
  });
}



/** listQueryAllCoreOrgTree POST /api/coreOrg/list */
export async function listQueryAllCoreOrgTreeUsingPost(
  body: CORE_ORG_API.CoreOrgQueryRequest,
  options?: { [key: string]: any }
) {
  return request<CORE_ORG_API.ResultListCoreOrgTreeVo_>("/author/coreOrg/list", {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    data: body,
    ...(options || {}),
  });
}


/** updateCoreOrg POST /api/coreOrg/update */
export async function updateCoreOrgUsingPost(
  body: CORE_ORG_API.CoreOrgUpdateRequest,
  options?: { [key: string]: any }
) {
  return request<API.ResultString_>("/author/coreOrg/update", {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    data: body,
    ...(options || {}),
  });
}
