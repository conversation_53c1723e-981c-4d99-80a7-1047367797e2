declare namespace ROLE_API {
  type RoleAddRequest = {
    description?: string;
    dsType?: string;
    name?: string;
    repel?: number;
    status?: number;
  };

  type ResultListRoleVO_ = {
    code?: number;
    data?: RoleVO[];
    description?: string;
    message?: string;
  };
  type RoleVO = {
    id?: number;
    name?: string;
  };

  type Role = {
    code?: string;
    createTime?: string;
    createUser?: number;
    description?: string;
    dsType?: string;
    id?: number;
    name?: string;
    readOnly?: number;
    repel?: number;
    status?: number;
    updateTime?: string;
    updateUser?: number;
  };

  type RoleQueryRequest = {
    current?: number;
    pageSize?: number;
    sortField?: string;
    sortOrder?: string;
    name?:string;
    repel?: number;
    status?: number;
  }
  type ResultPageRole_ = {
    code?: number;
    data?: PageRole_;
    description?: string;
    message?: string;
  };
  type PageRole_ = {
    current?: number;
    pages?: number;
    records?: Role[];
    size?: number;
    total?: number;
  };

  type RoleUpdateRequest = {
    id?: number;
    name?: string;
    repel?: number;
    status?: number;
  };

}

