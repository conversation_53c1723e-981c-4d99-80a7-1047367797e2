// @ts-ignore
/* eslint-disable */
import { request } from "@umijs/max";

/** addRole POST /api/role/add */
export async function addRoleUsingPost(
  body: ROLE_API.RoleAddRequest,
  options?: { [key: string]: any }
) {
  return request<API.ResultLong_>("/author/role/add", {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    data: body,
    ...(options || {}),
  });
}



/** listRoleVO GET /api/role/listVO */
export async function listRoleVoUsingGet(options?: { [key: string]: any }) {
  return request<ROLE_API.ResultListRoleVO_>("/author/role/listVO", {
    method: "GET",
    ...(options || {}),
  });
}


/** listRole GET /api/role/list */
export async function listRoleUsingGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: ROLE_API.RoleQueryRequest,
  options?: { [key: string]: any }
) {
  return request<ROLE_API.ResultPageRole_>("/author/role/list", {
    method: "GET",
    params: {
      ...params,
    },
    ...(options || {}),
  });
}


/** updateRole DELETE /api/role/update */
export async function updateRoleUsingUpdate(
  body: ROLE_API.RoleUpdateRequest,
  options?: { [key: string]: any }
) {
  return request<API.ResultString_>("/author/role/update", {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    data: body,
    ...(options || {}),
  });
}

/** deleteRole DELETE /api/role/delete */
export async function deleteRoleUsingDelete(
  body: API.DeleteRequest,
  options?: { [key: string]: any }
) {
  return request<API.ResultString_>("/author/role/delete", {
    method: "DELETE",
    headers: {
      "Content-Type": "application/json",
    },
    data: body,
    ...(options || {}),
  });
}

