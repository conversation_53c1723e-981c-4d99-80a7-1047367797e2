declare namespace USER_API {

  type UserLoginRequest = {
    account?: string;
    password?: string;
  };

  type ResultLoginUserVO_ = {
    code?: number;
    data?: LoginUserVO;
    description?: string;
    message?: string;
  };


  type LoginUserVO = {
    avatar?: string;
    id?: number;
    name?: string;
    sex?: string;
    workDescribe?: string;
  };


  type UserAddRequest = {
    account?: string;
    avatar?: string;
    mobile?: string;
    name?: string;
    roleId?: number;
    orgId?: string;
    sex?: string;
    stationId?: number;
    status?: number;
    superior?: number;
  };

  type UserQueryRequest = {
    current?: number;
    pageSize?: number;
    sortField?: string;
    sortOrder?: string;
    orgId?: number;
    stationId?: number;
    name?: string;
    mobile?: string;
    sex?: string;
    status?: number;
    superior?: number;
  };

  type ResultPageUser_ = {
    code?: number;
    data?: PageUser_;
    description?: string;
    message?: string;
  };

  type PageUser_ = {
    current?: number;
    pages?: number;
    records?: User[];
    size?: number;
    total?: number;
  };
  type User = {
    account?: string;
    administrator?: number;
    avatar?: string;
    createTime?: string;
    createUser?: number;
    email?: string;
    id?: number;
    isDelete?: number;
    lastLoginTime?: string;
    mobile?: string;
    name?: string;
    orgId?: number;
    password?: string;
    passwordErrorLastTime?: string;
    passwordErrorNum?: number;
    passwordExpireTime?: string;
    sex?: string;
    stationId?: number;
    status?: number;
    superior?: number;
    updateTime?: string;
    updateUser?: number;
    workDescribe?: string;
  };


  type UserVO = {
    id?: number;
    account?: string;
    name?: string;
    orgId?: number;
    orgName?: string;
    stationId?: number;
    stationName?: string;
    roleId?: number;
    orgTreeIds?: string[];
    roleName?: string;
    email?: string;
    mobile?: string;
    sex?: string;
    status?: number;
    avatar?: string;
    createTime?: string;
    updateTime?: string;

  };

  type PageUserVO_ = {
    current?: number;
    pages?: number;
    records?: UserVO[];
    size?: number;
    total?: number;
  };
  type ResultPageUserVO_ = {
    code?: number;
    data?: PageUserVO_;
    description?: string;
    message?: string;
  };


  type UserUpdateRequest = {
    id?: number;
    orgId?: number;
    stationId?: number;
    status?: number;
  };
}

