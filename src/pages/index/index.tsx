import React, {useEffect, useState} from 'react';
import {PageContainer} from "@ant-design/pro-components";
import {
  DownloadOutlined,
  EditOutlined,
  EllipsisOutlined,
  ShareAltOutlined,
} from '@ant-design/icons';
import {Avatar, Card, Dropdown, List, Tooltip,} from 'antd';
import {listGeneratorByPageUsingPost} from "@/services/generatorBackendAPI/generatorController";

const DEFAULT_PAGE_PARAMS:PageRequest = {
  current: 1,
  pageSize: 4,
  sortOrder: 'descend',
  sortField: 'createTime',
}
const IndexPage: React.FC = () => {
  //查询参数
  const [searchParms,setSearchParms] = useState<API.GeneratorQueryRequest>({...DEFAULT_PAGE_PARAMS})
  //查询结果
  const [dataList,setDataList] = useState<API.GeneratorVO[]>([]);
  //查询总数
  const [total,setTotal] = useState<number>(0)
  //定义数据是否加载中
  const [loading , setLoading] = useState<boolean>(false);

  const doSearch = async  () => {
      setLoading(true)
    await listGeneratorByPageUsingPost(searchParms)
  }
  //hook函数来监听searchParms的变化
  useEffect(()=>{

  },[searchParms])



  return (
    <PageContainer>
      <List<API.GeneratorVO>
        rowKey="id"
        grid={{
          gutter: 16,
          xs: 1,
          sm: 2,
          md: 3,
          lg: 3,
          xl: 4,
          xxl: 4,
        }}
        loading={loading}
        dataSource={undefined}
        renderItem={(item) => (
          <List.Item key={item.id}>
            <Card
              hoverable
              bodyStyle={{
                paddingBottom: 20,
              }}
              actions={[
                <Tooltip key="download" title="下载">
                  <DownloadOutlined />
                </Tooltip>,
                <Tooltip key="edit" title="编辑">
                  <EditOutlined />
                </Tooltip>,
                <Tooltip title="分享" key="share">
                  <ShareAltOutlined />
                </Tooltip>,
                <Dropdown
                  key="ellipsis"
                  menu={{
                    items: [
                      {
                        key: '1',
                        title: '1st menu item',
                      },
                      {
                        key: '2',
                        title: '2st menu item',
                      },
                    ],
                  }}
                >
                  <EllipsisOutlined />
                </Dropdown>,
              ]}
            >
              <Card.Meta
                avatar={<Avatar size="small" src={item.name} />}
                title={item.tags}
              />
              <div>
                {/*<CardInfo*/}
                {/*  activeUser={formatWan(item.activeUser)}*/}
                {/*  newUser={numeral(item.newUser).format('0,0')}*/}
                {/*/>*/}
              </div>
            </Card>
          </List.Item>
        )}
      />
    </PageContainer>
  )
}



export default IndexPage;

