// // import React, {useEffect, useState} from 'react';
// // import {PageContainer} from "@ant-design/pro-components";
// //
// // import {Avatar, Card, Dropdown, List, message, Tooltip,} from 'antd';
// //
// // import { DownOutlined, UpOutlined } from '@ant-design/icons';
// // import {
// //   ProForm,
// //   ProFormDatePicker,
// //   ProFormText,
// //   QueryFilter,
// // } from '@ant-design/pro-components';
// // import { Input, Tabs } from 'antd';
// //
// // import {
// //   listGeneratorVoByPageUsingPost
// // } from "@/services/generatorBackendAPI/generatorController";
// //
// // const DEFAULT_PAGE_PARAMS: PageRequest = {
// //   current: 1,
// //   pageSize: 4,
// //   sortOrder: 'descend',
// //   sortField: 'createTime',
// // }
// // const IndexPage: React.FC = () => {
//
// //   //查询参数
// //   const [searchParms, setSearchParms] = useState<API.GeneratorQueryRequest>({...DEFAULT_PAGE_PARAMS})
// //   //查询结果
// //   const [dataList, setDataList] = useState<API.GeneratorVO[]>([]);
// //   //查询总数
// //   const [total, setTotal] = useState<number>(0)
// //   //定义数据是否加载中
// //   const [loading, setLoading] = useState<boolean>(false);
// //
// //   const doSearch = async () => {
// //     setLoading(true)
// //     const res = await listGeneratorVoByPageUsingPost(searchParms)
// //     setDataList(res.data?.records ?? [])
// //     setLoading(false)
// //     setTotal(res.data?.total ?? 0)
// //     try {
// //
// //     }catch (err:any){
// //       message.error(err.description || err.message);
// //     }
// //   }
// //   //hook函数来监听searchParms的变化
// //   useEffect(() => {
// //     doSearch()
// //   }, [searchParms])
// //
// //
// //   return (
// //     <PageContainer>
// //       return (
// //       <div
// //         style={{
// //           padding: 24,
// //         }}
// //       >
// //         <div
// //           style={{
// //             display: 'flex',
// //             flexDirection: 'column',
// //             gap: 8,
// //           }}
// //         >
// //           <Input.Search
// //             placeholder="请输入"
// //             enterButton="搜索"
// //             value={123}
// //             // onChange={(e) => {
// //             //   setSearchText(e.target.value);
// //             // }}
// //             onSearch={()=>{}}
// //             style={{ maxWidth: 522, width: '100%' }}
// //           />
// //           <div
// //             style={{
// //               display: 'flex',
// //               gap: 12,
// //             }}
// //           >
// //           </div>
// //         </div>
// //
// //         <Tabs
// //           defaultActiveKey={defaultType}
// //           onChange={onTypeChange}
// //           tabBarExtraContent={
// //             <a
// //               style={{
// //                 display: 'flex',
// //                 gap: 4,
// //               }}
// //               onClick={() => {
// //                 setShowFilter(!showFilter);
// //               }}
// //             >
// //               高级筛选 {showFilter ? <UpOutlined /> : <DownOutlined />}
// //             </a>
// //           }
// //           items={[
// //             {
// //               key: 'articles',
// //               label: '文章',
// //             },
// //             {
// //               key: 'projects',
// //               label: '项目',
// //             },
// //             {
// //               key: 'applications',
// //               label: '应用',
// //             },
// //           ]}
// //         />
// //
// //         {showFilter ? (
// //           <QueryFilter
// //             submitter={false}
// //             span={24}
// //             labelWidth="auto"
// //             split
// //             onChange={onFilterChange}
// //           >
// //             <ProForm.Group title="姓名">
// //               <ProFormText name="name" />
// //             </ProForm.Group>
// //             <ProForm.Group title="详情">
// //               <ProFormText name="age" label="年龄" />
// //               <ProFormDatePicker name="birth" label="生日" />
// //             </ProForm.Group>
// //           </QueryFilter>
// //         ) : null}
// //       </div>
// //     </PageContainer>
// //   )
// // }
// //
// //
// //
// // export default IndexPage;
// //
// import {
//   DownloadOutlined,
//   DownOutlined,
//   EditOutlined,
//   EllipsisOutlined,
//   ShareAltOutlined,
//   UpOutlined,
//   UserOutlined
// } from '@ant-design/icons';
// import {
//   PageContainer,
//   ProFormSelect,
//   ProFormText,
//   QueryFilter,
// } from '@ant-design/pro-components';
//
// import {Avatar, Card, Input, message, Tabs, Image, Typography, Flex, Tag, Space} from 'antd';
// import React, {useEffect, useState} from 'react';
// import {listGeneratorVoByPageUsingPost} from "@/services/generatorBackendAPI/generatorController";
// import {List} from 'antd/lib';
// import moment from "moment";
//
//
// const DEFAULT_PAGE_PARAMS: PageRequest = {
//   current: 1,
//   pageSize: 4,
//   sortOrder: 'descend',
//   sortField: 'createTime',
// }
// const IndexPage: React.FC = () => {
//   //查询参数
//   const [searchParams, setSearchParams] = useState<API.GeneratorQueryRequest>({...DEFAULT_PAGE_PARAMS})
//   //查询结果
//   const [dataList, setDataList] = useState<API.GeneratorVO[]>([]);
//   //查询总数
//   const [total, setTotal] = useState<number>(0)
//   //定义数据是否加载中
//   const [loading, setLoading] = useState<boolean>(false);
//
//   //输入框
//   const [searchText, setSearchText] = useState<string>();
//   //高级筛选控制
//   const [showFilter, setShowFilter] = useState<boolean>(false);
//
//   const doSearch = async () => {
//     setLoading(true)
//     const res = await listGeneratorVoByPageUsingPost(searchParams)
//     setDataList(res.data?.records ?? [])
//     setTotal(res.data?.total ?? 0)
//     setLoading(false)
//     try {
//     } catch (err: any) {
//       message.error(err.description || err.message);
//     }
//   }
//   //hook函数来监听searchParms的变化
//   useEffect(() => {
//     console.log('searchParams', searchParams)
//     doSearch()
//   }, [searchParams])
//
//   /**
//    * 标签列表
//    * @param tags
//    */
//   const tagListView = (tags?: string[]) => {
//     if (!tags) {
//       return <></>
//     }
//
//     return <div style={{marginBottom: 16}}>{tags.map((tag) => <Tag key={tag}>{tag}</Tag>)}</div>
//
//   }
//   return (
//     <PageContainer>
//       <div
//         style={{
//           padding: 24,
//         }}
//       >
//         <div
//           style={{
//             display: 'flex',
//             flexDirection: 'column',
//             gap: 8,
//           }}
//         >
//           <Input.Search
//             placeholder="请输入"
//             enterButton="搜索"
//             value={searchText}
//             onChange={(e) => {
//               setSearchText(e.target.value);
//             }}
//             onSearch={(value: string) => {
//               setSearchParams({
//                 ...searchParams,
//                 ...DEFAULT_PAGE_PARAMS,
//                 searchText
//               })
//             }}
//             style={{maxWidth: 522, width: '100%', margin: '0 auto'}}
//           />
//           <div
//             style={{
//               display: 'flex',
//               gap: 12,
//             }}
//           >
//           </div>
//         </div>
//         <Tabs
//           defaultActiveKey={"articles"}
//           onChange={() => {
//           }}
//           tabBarExtraContent={
//             <a
//               style={{
//                 display: 'flex',
//                 gap: 4,
//               }}
//               onClick={() => {
//                 setShowFilter(!showFilter);
//               }}
//             >
//               高级筛选 {showFilter ? <UpOutlined/> : <DownOutlined/>}
//             </a>
//           }
//           items={[
//             {
//               key: 'articles',
//               label: '文章',
//             },
//             {
//               key: 'projects',
//               label: '项目',
//             },
//             {
//               key: 'applications',
//               label: '应用',
//             },
//           ]}
//         />
//
//         {showFilter ? (
//           <QueryFilter
//             submitter={{
//               searchConfig: {
//                 submitText: '确认',
//                 resetText: '重置',
//               },
//               submitButtonProps: {
//                 style: {
//                   marginLeft: 5
//                 }
//               }
//             }}
//             span={6}
//             labelWidth="auto"
//             split
//             onChange={(e: any) => {
//               console.log('change', e.target.id)
//             }}
//
//             onFinish={async (values: API.GeneratorQueryRequest) => {
//               console.log(values)
//               setSearchParams({
//                 ...DEFAULT_PAGE_PARAMS,
//                 ...values,
//                 searchText
//               });
//             }}
//
//           >
//             <ProFormSelect name="tags" label="标签" mode="tags"/>
//             <ProFormText name="name" label="名称"/>
//             <ProFormText name="description" label="描述"/>
//           </QueryFilter>
//         ) : null}
//       </div>
//       <br/>
//       <List<API.GeneratorVO>
//         rowKey="id"
//         loading={loading}
//         grid={{
//           gutter: 30,  // 卡片间距
//           xs: 1,       // 超小屏幕每行个
//           sm: 2,       // 小屏幕每行个
//           md: 3,       // 中等屏幕每行个
//           lg: 3,       // 大屏幕每行个
//           xl: 4,       // 超大屏幕每行个
//           xxl: 4,      // 超超大屏幕每行个
//         }}
//         dataSource={dataList}
//         //分页处理
//         pagination={{
//           current: searchParams.current,
//           pageSize: searchParams.pageSize,
//           total: total,
//           onChange(current, pageSize) {
//             setSearchParams({
//               ...searchParams,
//               current,
//               pageSize,
//             })
//           }
//         }}
//         renderItem={(data) => (
//           <List.Item>
//             <Card hoverable cover={<Image alt={data.name} src={data.picture}/>}>
//               <Card.Meta
//                 title={<a>{data.name}</a>}
//                 description={
//                   <Typography.Paragraph ellipsis={{rows: 2}} style={{height: 44}}>
//                     {data.description}
//                   </Typography.Paragraph>
//                 }
//               />
//               {tagListView(data.tags)}
//               <Flex justify={"space-between"} align={"center"}>
//                 <span>{moment(data.createTime).fromNow()}</span>
//                 <div>
//                   <Avatar size="large" src={data.user?.avatarUrl ?? <UserOutlined/>}/>
//                 </div>
//               </Flex>
//             </Card>
//           </List.Item>
//         )}
//       />
//     </PageContainer>
//   );
// };
//
// export default IndexPage;
