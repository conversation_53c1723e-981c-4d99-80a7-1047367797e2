import {Footer} from '@/components';
import {
  AlipayCircleOutlined,
  LockOutlined,
  TaobaoCircleOutlined,
  UserOutlined,
  WeiboCircleOutlined,
} from '@ant-design/icons';
import {
  LoginForm,
  ProFormText,
} from '@ant-design/pro-components';
import {Helmet, useModel, history, Link} from '@umijs/max';
import {Tabs, message} from 'antd';
import {createStyles} from 'antd-style';
import React, {useState} from 'react';
import {userLoginUsingPost} from "@/services/logistics-backend-api/user/userController";
import Settings from "../../../../config/defaultSettings";

//登录页面的一些样式
const useStyles = createStyles(({token}) => {
  return {
    action: {
      marginLeft: '8px',
      color: 'rgba(0, 0, 0, 0.2)',
      fontSize: '24px',
      verticalAlign: 'middle',
      cursor: 'pointer',
      transition: 'color 0.3s',
      '&:hover': {
        color: token.colorPrimaryActive,
      },
    },
    container: {
      display: 'flex',
      flexDirection: 'column',
      height: '100vh',
      overflow: 'auto',
      backgroundImage:
        "url('https://mdn.alipayobjects.com/yuyan_qk0oxh/afts/img/V-_oS6r-i7wAAAAAAAAAAAAAFl94AQBr')",
      backgroundSize: '100% 100%',
    },
  };
});



const Login: React.FC = () => {
  const [type, setType] = useState<string>('account');
  const {initialState, setInitialState} = useModel('@@initialState');
  const {styles} = useStyles();


  const handleSubmit = async (values: USER_API.UserLoginRequest) => {
    try {
      // 登录
      const res = await userLoginUsingPost({...values});
      const defaultLoginSuccessMessage = '登录成功!';
      message.success(defaultLoginSuccessMessage);

      // 保存已登录用户信息
      setInitialState({...initialState, currentUser: res.data});
      /**
       * 拿取当前URL路径，并且取所有的参数key:value存储
       */
      const urlParams = new URL(window.location.href).searchParams;
      /**
       * 获取redirect参数对应的value,然后跳转路径为空跳转首页
       * 代码的作用就是当用户访问需要登录的操作时，会被带到登录,当登录成功需要带回之前访问的页面
       */
      const redirectUrl = urlParams.get('redirect');
      history.push(redirectUrl ? decodeURIComponent(redirectUrl) : '/');
      return;
    } catch (error: any) {
      // message为空读取description
      const defaultLoginFailureMessage = error.description && error.description.trim() ? error.description : error.message;
      message.error(`登录失败  ${defaultLoginFailureMessage}`);
    }
  };
  return (
    <div className={styles.container}>
      <Helmet>
        <title>
          {'登录'}
          {Settings.title && ` - ${Settings.title}`}
        </title>
      </Helmet>
      <div
        style={{
          flex: '1',
          padding: '32px 0',
        }}
      >
        <LoginForm
          contentStyle={{
            minWidth: 280,
            maxWidth: '75vw',
          }}
          logo={<img alt="logo" src="/logo.svg"/>}
          title="Logistics ~"
          subTitle={'神领物流是物流界最具有影响力的Web服务'}
          initialValues={{
            autoLogin: true,
          }}
          onFinish={async (values) => {
            await handleSubmit(values as USER_API.UserLoginRequest);
          }}
        >
          <Tabs
            activeKey={type}
            onChange={setType}
            centered
            items={[
              {
                key: 'account',
                label: '账户密码登录',
              }
            ]}
          />
          {type === 'account' && (
            <>
              <ProFormText
                name="account"
                fieldProps={{
                  size: 'large',
                  prefix: <UserOutlined/>,
                }}
                placeholder={'Account:'}
                rules={[
                  {
                    required: true,
                    message: 'Account are required！',
                  },
                ]}
              />
              <ProFormText.Password
                name="password"
                fieldProps={{
                  size: 'large',
                  prefix: <LockOutlined/>,
                }}
                placeholder={'password: '}
                rules={[
                  {
                    required: true,
                    message: 'Passwords are required！',
                  },
                ]}
              />
            </>
          )}

          <div
            style={{
              marginBottom: 12,
            }}
          >

          </div>
        </LoginForm>
      </div>
      <Footer/>
    </div>
  );
};
export default Login;
