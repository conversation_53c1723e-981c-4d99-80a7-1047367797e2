import {Footer} from '@/components';
import {
  AlipayCircleOutlined,
  LockOutlined,
  MobileOutlined,
  TaobaoCircleOutlined,
  UserOutlined,
  WeiboCircleOutlined,
} from '@ant-design/icons';
import {
  LoginForm,
  ProFormText,
} from '@ant-design/pro-components';
import {Helmet, useModel, history, Link} from '@umijs/max';
import {Tabs, message} from 'antd';
import {createStyles} from 'antd-style';
import React, {useState} from 'react';
import Settings from '../../../../config/defaultSettings';
import {userLoginUsingPost, userRegisterUsingPost} from "@/services/generatorBackendAPI/userController";

//登录页面的一些样式
const useStyles = createStyles(({token}) => {
  return {
    action: {
      marginLeft: '8px',
      color: 'rgba(0, 0, 0, 0.2)',
      fontSize: '24px',
      verticalAlign: 'middle',
      cursor: 'pointer',
      transition: 'color 0.3s',
      '&:hover': {
        color: token.colorPrimaryActive,
      },
    },
    container: {
      display: 'flex',
      flexDirection: 'column',
      height: '100vh',
      overflow: 'auto',
      backgroundImage:
        "url('https://mdn.alipayobjects.com/yuyan_qk0oxh/afts/img/V-_oS6r-i7wAAAAAAAAAAAAAFl94AQBr')",
      backgroundSize: '100% 100%',
    },
  };
});

const UserRegister: React.FC = () => {
  const [type, setType] = useState<string>('account');
  const {styles} = useStyles();


  const handleSubmit = async (values: API.UserRegisterRequest) => {
    try {
      // 注册
      await userRegisterUsingPost({...values});
      const defaultLoginSuccessMessage = '注册成功!';
      message.success(defaultLoginSuccessMessage);
      history.push("/user/login")
      return;
    } catch (error: any) {
      const defaultLoginFailureMessage = error.description && error.description.trim() ? error.description : error.message;
      message.error(`注册失败  ${defaultLoginFailureMessage}`);
    }
  };
  return (
    <div className={styles.container}>
      <Helmet>
        <title>
          {'注册'}--{Settings.title}
        </title>
      </Helmet>
      <div
        style={{
          flex: '1',
          padding: '32px 0',
        }}
      >
        <LoginForm
          contentStyle={{
            minWidth: 280,
            maxWidth: '75vw',
          }}
          logo={<img alt="logo" src="/logo.svg"/>}
          title="Code Generator"
          subTitle={'Code Generator 是ChangSha最具影响力的 Web 设计规范'}
          initialValues={{
            autoLogin: true,
          }}
          onFinish={async (values) => {
            await handleSubmit(values as API.UserLoginRequest);
          }}

          submitter={{
            searchConfig: {
              submitText: '注册',
            },
          }}
        >
          <Tabs
            activeKey={type}
            onChange={setType}
            centered
            items={[
              {
                key: 'account',
                label: '新用户注册',
              }
            ]}
          />
          {type === 'account' && (
            <>
              <ProFormText
                name="userAccount"
                fieldProps={{
                  size: 'large',
                  prefix: <UserOutlined/>,
                }}
                placeholder={'Account:'}
                rules={[
                  {
                    required: true,
                    message: 'Account are required！',
                  },
                ]}
              />
              <ProFormText.Password
                name="userPassword"
                fieldProps={{
                  size: 'large',
                  prefix: <LockOutlined/>,
                }}
                placeholder={'userPassword: '}
                rules={[
                  {
                    required: true,
                    message: 'Passwords are required！',
                  },
                ]}
              />
              <ProFormText.Password
                name="checkPassword"
                fieldProps={{
                  size: 'large',
                  prefix: <LockOutlined/>,
                }}
                placeholder={'checkPassword: '}
                rules={[
                  {
                    required: true,
                    message: 'checkPassword are required！',
                  },
                ]}
              />
            </>
          )}

          <div
            style={{
              marginBottom: 12,
            }}
          >
            <Link to="/user/login">
              老用户登录
            </Link>
          </div>
        </LoginForm>
      </div>
      <Footer/>
    </div>
  );
};
export default UserRegister;
