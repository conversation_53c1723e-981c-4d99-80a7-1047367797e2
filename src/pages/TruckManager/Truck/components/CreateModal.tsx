import {ProColumns, ProTable} from '@ant-design/pro-components';
import '@umijs/max';
import {message, Modal} from 'antd';
import React from 'react';
import {addUsingPost} from "@/services/logistics-backend-api/truckType/truckTypeController";

interface Props {
  visible: boolean;
  columns: ProColumns<TRUCK_TYPE_API.TruckTypeAddRequest>[];
  onSubmit: (values: TRUCK_TYPE_API.TruckTypeAddRequest) => void;
  onCancel: () => void;
}

/**
 * 添加节点
 * @param fields
 */
const handleAdd = async (fields: TRUCK_TYPE_API.TruckTypeAddRequest) => {
  const hide = message.loading('正在添加');
  try {
    await addUsingPost(fields);
    hide();
    message.success('创建成功');
    return true;
  } catch (error: any) {
    hide();
    message.error('创建失败，' + error.message);
    return false;
  }
};

/**
 * 创建弹窗
 * @param props
 * @constructor
 */
const CreateModal: React.FC<Props> = (props) => {
  const {visible, columns, onSubmit, onCancel} = props;

  return (
    <Modal
      destroyOnClose
      title={'创建'}
      open={visible}
      footer={null}
      onCancel={() => {
        onCancel?.();
      }}
    >
      <ProTable
        type="form"
        columns={columns}
        onSubmit={async (values: TRUCK_TYPE_API.TruckTypeAddRequest) => {
          const success = await handleAdd(values);
          if (success) {
            onSubmit?.(values);
          }
        }}
      />
    </Modal>
  );
};
export default CreateModal;
