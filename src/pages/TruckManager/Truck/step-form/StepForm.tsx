import {
  PageContainer,
  ProForm, ProFormDatePicker,
  ProFormDigit,
  ProFormSelect,
  ProFormText,
  StepsForm,
  ProFormUploadButton, ProFormUploadDragger, ProColumns
} from '@ant-design/pro-components';
import {FormInstance, message, type UploadFile} from 'antd';
import {
  <PERSON><PERSON>,
  Button,
  Card,
  Divider,
  Result,
} from 'antd';
import React, {useEffect, useRef, useState} from 'react';
import {useParams} from "react-router";
import {getByIdUsingGet} from "@/services/logistics-backend-api/base/truck/truckController";
import {getAllVoUsingGet} from "@/services/logistics-backend-api/base/truckType/truckTypeController";
import FileText from "@/pages/file/FileText";
import {editUsingPost} from "@/services/logistics-backend-api/base/truckLicense/truckLicenseController";
import {COS_HOST} from "@/constants";

//车辆行驶证信息
// const StepDescriptions: React.FC<{ stepData: TRUCK_API.TruckEditRequest; bordered?: boolean; }> = ({ stepData, bordered }) => {
//
//   const { id,licensePlate,truckTypeId,allowableLoad, allowableVolume, deviceGpsId,pictures } = stepData;
//   return (
//     <Descriptions column={1} bordered={bordered}>
//       <Descriptions.Item label="车辆编号"> {id}</Descriptions.Item>
//       <Descriptions.Item label="车牌号"> {licensePlate}</Descriptions.Item>
//       <Descriptions.Item label="车型名称"> {truckTypeId}</Descriptions.Item>
//       <Descriptions.Item label="车辆载重"> {allowableLoad}</Descriptions.Item>
//       <Descriptions.Item label="车辆体积"> {allowableVolume}</Descriptions.Item>
//       <Descriptions.Item label="GPS"> {deviceGpsId}</Descriptions.Item>
//     </Descriptions>
//   );
// };

const StepResult: React.FC<{ onFinish: () => Promise<void>; children?: React.ReactNode; }> = (props) => {
  return (
    <Result
      status="success"
      title="操作成功"
      subTitle="预计两小时内到账"
      extra={
        <>
          <Button type="primary" onClick={props.onFinish}>
            再转一笔
          </Button>
          <Button>查看账单</Button>
        </>
      }
    >
      {props.children}
    </Result>
  );
};

const StepForm: React.FC<Record<string, any>> = () => {
  const params = useParams<{ id: string }>()
  const [stepData, setStepData] = useState<TRUCK_API.TruckEditRequest>();
  const [truckType, setTruckType] = useState<TRUCK_TYPE_API.TruckTypeVO[]>();
  const initializeTruckType = async () => {
    try {
      const {data} = await getAllVoUsingGet()
      setTruckType(data)
    } catch (error: any) {
      message.error(error.message)
    }
  }
  useEffect(() => {
    initializeTruckType().then(() => {
    })
  }, [])

  const [imageUrls, setImageUrls] = useState<string[]>([]);

  const handleChange = (urlList: string[]) => {
    setImageUrls(urlList);
    console.log('当前所有图片URL:', urlList);
  };

  const [current, setCurrent] = useState(0);
  const formRef = useRef<FormInstance>(null);
  return (
    <PageContainer content="将一个冗长或用户不熟悉的表单任务分成多个步骤，指导用户完成。">
      <Card bordered={false}>
        <StepsForm
          current={current}
          onCurrentChange={setCurrent}
          submitter={{
            render: (props, dom) => {
              if (props.step === 2) {
                return null;
              }
              return dom;
            },
          }}
        >
          {/*分步表单的第一步*/}
          <StepsForm.StepForm<TRUCK_API.TruckEditRequest>
            formRef={formRef}
            title="车辆基本信息"
            onFinish={async (values) => {
              values = {...values, pictures: imageUrls}
              console.log("车辆基本信息修改", values)
              try {
                // await editUsingPost(values)
              } catch (e: any) {
                message.error("上传失败" + e.description && e.description.trim() ? e.description : e.message)
                return false;
              }
              // setStepData(values);
              return true;
            }}
            request={async () => {
              if (!params.id) return {};
              try {
                const {data} = await getByIdUsingGet({id: params.id});
                setStepData(data);
                setImageUrls(data?.pictures || [])
                return data || {};
              } catch (error) {
                console.error('获取数据失败:', error);
                return {};
              }
            }}
          >

            <ProForm.Group size={32}>
              <ProFormText
                label="车辆编号"
                width="md"
                name="id"
                fieldProps={{
                  readOnly: true,
                  style: {
                    backgroundColor: '#f5f5f5',
                    color: '#5E5E5E',
                  },
                }}
              />
              <ProFormText
                label="车牌号码"
                width="md"
                name="licensePlate"
                fieldProps={{
                  readOnly: true,
                  style: {
                    backgroundColor: '#f5f5f5',
                    color: '#5E5E5E',
                  },
                }}
              />
              <ProFormSelect
                label="车型名称"
                name="truckTypeId"
                rules={[
                  {
                    required: true,
                    message: '请选择车辆类型',
                  },
                ]}
                options={truckType?.map(item => ({
                  label: item.name,
                  value: item.id
                }))}
              />
            </ProForm.Group>
            <ProForm.Group size={32}>
              <ProFormDigit
                label="车辆体积 m³"
                width="md"
                name="allowableVolume"
                fieldProps={
                  {
                    precision: 2,
                    max: 1000,
                    min: 1
                  }
                }
                rules={[
                  {
                    required: true,
                    message: '请选择车辆体积',
                  },
                ]}
              />
              <ProFormDigit
                label="车辆载重 t"
                width="md"
                name="allowableLoad"
                fieldProps={
                  {
                    precision: 2,
                    max: 1000,
                    min: 1
                  }
                }
                rules={[
                  {
                    required: true,
                    message: '请选择车辆载重',
                  },
                ]}
              />
              <ProFormText
                label="GPSID"
                width="md"
                name="deviceGpsId"
                fieldProps={{
                  readOnly: true,
                  style: {
                    backgroundColor: '#f5f5f5',
                    color: '#5E5E5E',
                  },
                }}
              />
            </ProForm.Group>
            <ProForm.Group title={"上传图片"} size={30}>
              <FileText
                visible={imageUrls}
                onChange={handleChange}
              />

            </ProForm.Group>

          </StepsForm.StepForm>
          {/*分步表单的第二步*/}
          <StepsForm.StepForm<TRUCK_LICENSE_API.editUsingPOST1Params>
            title="车辆行驶证信息"
            onFinish={async (values) => {
              console.log("车辆行驶证信息修改", values)
              setStepData(values);
              return true;
            }}>
            <div>
              <Alert
                closable
                showIcon
                message="温馨提示! 完成好车辆行驶证信息后 请确认检查后提交 🤣 ~"
                style={{
                  marginBottom: 24,
                }}
              />
              {/*<StepDescriptions stepData={stepData} bordered />*/}
              <Divider
                style={{
                  margin: '24px 0',
                }}
              />
              <ProForm.Group size={16}>
                <ProFormDatePicker
                  label="注册时间"
                  width="md"
                  name="registrationDate"
                  rules={[
                    {
                      required: true,
                      message: '请填写车辆注册时间',
                    },
                  ]}
                />
                <ProFormDatePicker
                  label="国家强制报废日期"
                  width="md"
                  name="mandatoryScrap"
                  rules={[
                    {
                      required: true,
                      message: '请填写强制报废日期',
                    },
                  ]}
                />
                <ProFormDatePicker
                  label="检验有效期"
                  width="md"
                  name="expirationDate"
                  rules={[
                    {
                      required: true,
                      message: '请填写检验有效期',
                    },
                  ]}
                />

              </ProForm.Group>
              <ProForm.Group size={16}>

                <ProFormDatePicker
                  label="行驶证有效期"
                  width="md"
                  name="validityPeriod"
                  rules={[
                    {
                      required: true,
                      message: '请填写行驶证有效期',
                    },
                  ]}
                />

                <ProFormText
                  label="行驶证号"
                  width="md"
                  name="transportCertificateNumber"
                  rules={[
                    {
                      required: true,
                      message: '请填写行驶证号',
                    },
                  ]}
                />
                <ProFormText
                  label="发动机编号"
                  width="md"
                  name="engineNumber"
                  rules={[
                    {
                      required: true,
                      message: '请填写发动机编号',
                    },
                  ]}
                />
              </ProForm.Group>

              <ProForm.Group size={16}>

                <ProFormDigit
                  label="整备质量 (T)"
                  width="md"
                  name="overallQuality"
                  fieldProps={
                    {
                      precision: 2,
                      max: 1000,
                      min: 1
                    }
                  }
                  rules={[
                    {
                      required: true,
                      message: '请填写整备质量',
                    },
                  ]}
                />

                <ProFormDigit
                  label="核定载质量 (KG)"
                  width="md"
                  name="allowableWeight"
                  fieldProps={
                    {
                      precision: 2,
                      max: 1000,
                      min: 1
                    }
                  }
                  rules={[
                    {
                      required: true,
                      message: '请输入核定载质量',
                    },
                  ]}
                />
              </ProForm.Group>

              <ProForm.Group title={"上传图片"} size={16}>

              </ProForm.Group>


            </div>


          </StepsForm.StepForm>
          <StepsForm.StepForm title="完成">
            <StepResult
              onFinish={async () => {
                console.log("完成")
                setCurrent(0);
                formRef.current?.resetFields();
              }}
            >
              {/*<StepDescriptions stepData={stepData} />*/}
            </StepResult>
          </StepsForm.StepForm>
        </StepsForm>
        <Divider
          style={{
            margin: '40px 0 24px',
          }}
        />
        <div>
          <h3>说明</h3>
          <h4>转账到支付宝账户</h4>
          <p>
            如果需要，这里可以放一些关于产品的常见问题说明。如果需要，这里可以放一些关于产品的常见问题说明。如果需要，这里可以放一些关于产品的常见问题说明。
          </p>
          <h4>转账到银行卡</h4>
          <p>
            如果需要，这里可以放一些关于产品的常见问题说明。如果需要，这里可以放一些关于产品的常见问题说明。如果需要，这里可以放一些关于产品的常见问题说明。
          </p>
        </div>
      </Card>
    </PageContainer>
  );
};
export default StepForm;
