import React, {useEffect, useRef, useState} from "react";
import {type ActionType, PageContainer, ProColumns, ProTable} from "@ant-design/pro-components";
import {listUsingPost} from "@/services/logistics-backend-api/truck/truckController";
import {Badge, Button, message, Modal, Space, Tag, Typography} from "antd";
import {PlusOutlined} from "@ant-design/icons";
import CreateModal from "@/pages/TruckManager/Truck/components/CreateModal";
import {getAllVoUsingGet} from "@/services/logistics-backend-api/truckType/truckTypeController";


const truckPage: React.FC = () => {
  //当前数据
  const [currentRow, setCurrentRow] = useState<TRUCK_API.TruckListVO>();
  //新建窗口的显示与否
  const [createModalVisible, setCreateModalVisible] = useState<boolean>(false);
  //更新弹窗的显示与否
  const [updateModalVisible, setUpdateModalVisible] = useState<boolean>(false);

  //初始化车辆类型
  const [truckTypeList, setTruckTypeList] = useState<TRUCK_TYPE_API.TruckType[]>([]);
  const initialTruckTypeList = async () => {
    const {data} = await getAllVoUsingGet()
    if (data) {
      setTruckTypeList(data || []);
    }
  }
  useEffect(() => {
    initialTruckTypeList().then(() => {
    })
  }, []);

  // @ts-ignore TODO
  const actionRef = useRef<ActionType>();

  const handleDelete = async (row: TRUCK_API.TruckListVO) => {
    console.log(row);
    Modal.confirm({
      title: '确认删除',
      content: `确定要删除车牌号为 "${row.licensePlate}" 的车辆吗？此操作不可恢复。`,
      okText: '确认',
      cancelText: '取消',
      onOk: async () => {
        const hide = message.loading('正在删除');
        if (!row) return true;
        try {
          // await  deleteUsingDelete(row)
          hide();
          message.success('删除成功');
          actionRef?.current?.reload();
          return true;
        } catch (error: any) {
          hide();
          message.error('删除失败，' + error.message);
          return false;
        }
      },
    })
  };

  const columns: ProColumns<TRUCK_API.TruckListVO>[] = [
    {
      title: '车辆编号',
      dataIndex: 'id',
      valueType: 'text',
      hideInSearch: true,
      hideInForm: true,
    },
    {
      title: '车牌号',
      dataIndex: 'licensePlate',
      valueType: 'text',
      formItemProps: {
        rules: [
          {
            required: true,
            message: '此项为必填项',
          },
        ],
      },
    },
    {
      title: 'GPS编号',
      dataIndex: 'deviceGpsId',
      valueType: 'text',
      formItemProps: {
        rules: [
          {
            required: true,
            message: '此项为必填项',
          },
        ],
      },
    },
    {
      title: '车辆类型',
      dataIndex: 'truckTypeId',
      valueType: 'select',
      hideInForm: true,
      fieldProps: {
        options: truckTypeList.map((item) => ({
          label: item.name,
          value: item.id,
        })),
      },
      render: (_, record) => {
        const truckType = truckTypeList.find(item => item.id === record.truckTypeId);
        return truckType ? <Tag color="#5489F5">{truckType.name}</Tag> : <></>;
      },
    },
    {
      title: '准载重量 (t)',
      dataIndex: 'allowableLoad',
      valueType: 'text',
      hideInSearch: true,
      hideInForm: true,
      render(_, record) {
        if (!record.allowableLoad) {
          return <></>;
        }
        return <Tag key={record.allowableLoad}>{record.allowableLoad}</Tag>
      }
    },
    {
      title: '准载体积 (平方m)',
      dataIndex: 'allowableLoad',
      valueType: 'text',
      hideInSearch: true,
      hideInForm: true,
      render(_, record) {
        if (!record.allowableLoad) {
          return <></>;
        }
        return <Tag key={record.allowableLoad}>{record.allowableLoad}</Tag>
      }
    },
    {
      title: '工作状态',
      dataIndex: 'workStatus',
      hideInSearch: true,
      hideInForm: true,
      fieldProps: {
        options: [
          {label: '禁用', value: 0},
          {label: '启用', value: 1},
        ],
      },
      render: (_, record) => {
        return record.workStatus === 1 ? <Badge status="success" text="工作中"/> :
          <Badge status="error" text="停用中"/>;
      }
    },
    {
      title: '车辆状态',
      dataIndex: 'status',
      hideInSearch: true,
      hideInForm: true,
      fieldProps: {
        options: [
          {label: '禁用', value: 0},
          {label: '启用', value: 1},
        ],
      },
      render: (_, record) => {
        return record.status === 1 ? <Badge status="success" text="可用"/> : <Badge status="error" text="不可用"/>;
      }
    },
    {
      title: '操作',
      dataIndex: 'option',
      valueType: 'option',
      render: (_, record) => (
        <Space size="middle">
          <Typography.Link
            onClick={() => {
              setCurrentRow(record);
              setUpdateModalVisible(true);
            }}
          >
            修改
          </Typography.Link>
          <Typography.Link type="danger" onClick={() => {
          }}>
            删除
          </Typography.Link>
        </Space>
      ),
    },
  ]
  return (
    <PageContainer>
      <ProTable<TRUCK_API.TruckListVO>
        headerTitle={'车辆表格'}
        actionRef={actionRef}
        rowKey={(record) => record.id || Math.random().toString()}
        search={{
          labelWidth: 120,
        }}
        toolBarRender={() => [
          <Button
            type="primary"
            key="primary"
            onClick={() => {
              setCreateModalVisible(true);
            }}
          >
            <PlusOutlined/> 新建
          </Button>,
        ]}
        request={async (params, sort, filter) => {
          //拿取排序字段
          const sortField = Object.keys(sort)?.[0];
          //拿取排序方式 ascend/descend
          const sortOrder = sort?.[sortField] ?? undefined;
          const {code, data} = await listUsingPost({
            ...params,
            sortField,
            sortOrder,
            ...filter,
          } as TRUCK_API.TruckQueryRequest)
          return {
            data: data?.records || [],
            success: code === 0,
            total: Number(data?.total) || 0,
          };
        }}
        columns={columns}
      />
      <CreateModal
        visible={createModalVisible}
        columns={columns}
        onSubmit={() => {
          setCreateModalVisible(false);
          actionRef.current?.reload();
        }}
        onCancel={() => {
          setCreateModalVisible(false);
        }}
      />

      {/*<UpdateModal*/}
      {/*  visible={updateModalVisible}*/}
      {/*  columns={columns}*/}
      {/*  oldData={currentRow}*/}
      {/*  onSubmit={() => {*/}
      {/*    setUpdateModalVisible(false);*/}
      {/*    setCurrentRow(undefined);*/}
      {/*    // reload后会调用request方法*/}
      {/*    actionRef.current?.reload();*/}
      {/*  }}*/}
      {/*  onCancel={() => {*/}
      {/*    setUpdateModalVisible(false);*/}
      {/*  }}*/}
      {/*/>*/}
    </PageContainer>
  );
};
export default truckPage;
