import React, { useState } from 'react';
import { Upload } from 'antd';
import type { GetProp, UploadFile, UploadProps } from 'antd';
import ImgCrop from 'antd-img-crop';
import {COS_HOST} from "@/constants";
import {truckUploadFileUsingPost} from "@/services/logistics-backend-api/file/fileController";

type FileType = Parameters<GetProp<UploadProps, 'beforeUpload'>>[0];
//传递上传组件的值
interface Props {
  truckImageUrls: string[]; //初始图片
  onChange?: (urlList: string[]) => void //回调函数
}
const TruckUpload: React.FC<Props> = (props) => {
  const {truckImageUrls, onChange} = props;


  const [fileList, setFileList] = useState<UploadFile[]>(() => {
    // 确保 truckImageUrls 已定义，或者使用其他数据源
      return truckImageUrls.map((item, index) => ({
        uid: index.toString(),
        name: item.substring(item.lastIndexOf('/') + 1) || `image-${index}.png`,
        status: 'done',
        url: COS_HOST + item,
        response: item,
      }));
  });
  //自定义上传
  const customRequest = async ({ file, onSuccess }: any) => {
    const result = await truckUploadFileUsingPost({}, file);
    console.log("result.data",result.data)
    onSuccess(result.data);
  };

  //上传后触发事件
  const handleChange: UploadProps['onChange'] = ({ fileList: newFileList }) => {
    setFileList(newFileList);
    const uploadedUrls = newFileList
      .filter(file => file.status === 'done')
      .map(file => file.response) // 根据您的API响应结构调整
    // 回调给父组件
    if (onChange) {
      onChange(uploadedUrls);
    }
  };

  const onPreview = async (file: UploadFile) => {
    let src = file.url as string;
    if (!src) {
      src = await new Promise((resolve) => {
        const reader = new FileReader();
        reader.readAsDataURL(file.originFileObj as FileType);
        reader.onload = () => resolve(reader.result as string);
      });
    }
    const image = new Image();
    image.src = src;
    const imgWindow = window.open(src);
    imgWindow?.document.write(image.outerHTML);
  };

  return (
    <ImgCrop rotationSlider>
      <Upload
        customRequest={customRequest}
        listType="picture-card"
        fileList={fileList}
        onChange={handleChange}
        onPreview={onPreview}
      >
        {fileList.length < 5 && '+ Upload'}
      </Upload>
    </ImgCrop>
  );
};

export default TruckUpload;
