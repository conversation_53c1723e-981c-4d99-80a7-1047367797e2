import React, { useState } from 'react';
import { Upload, message } from 'antd';
import type { GetProp, UploadFile, UploadProps } from 'antd';
import { PlusOutlined } from '@ant-design/icons';

type FileType = Parameters<GetProp<UploadProps, 'beforeUpload'>>[0];

// 传递上传组件的值
interface Props {
  driverLicenseImageUrls: string[]; // 初始图片
  onChange?: (urlList: string[]) => void; // 回调函数
}

const DriverLicenseUpload: React.FC<Props> = (props) => {
  const { driverLicenseImageUrls, onChange } = props;

  const [fileList, setFileList] = useState<UploadFile[]>(() => {
    // 将传入的图片URL转换为UploadFile格式
    return driverLicenseImageUrls.map((item, index) => ({
      uid: index.toString(),
      name: `驾驶证图片-${index + 1}.jpg`,
      status: 'done',
      url: item,
      response: item,
    }));
  });

  // 自定义上传处理（这里暂时不实际上传到服务器）
  const customRequest = async ({ file, onSuccess }: any) => {
    // 模拟上传成功，实际项目中这里应该调用真实的上传API
    setTimeout(() => {
      // 生成一个临时的预览URL
      const reader = new FileReader();
      reader.onload = () => {
        onSuccess(reader.result);
      };
      reader.readAsDataURL(file);
    }, 1000);
  };

  // 上传前的验证
  const beforeUpload = (file: FileType) => {
    // 检查文件类型
    const isImage = file.type.startsWith('image/');
    if (!isImage) {
      message.error('只能上传图片文件!');
      return false;
    }

    // 检查文件大小 (5MB)
    const isLt5M = file.size / 1024 / 1024 < 5;
    if (!isLt5M) {
      message.error('图片大小不能超过 5MB!');
      return false;
    }

    // 检查数量限制
    if (fileList.length >= 3) {
      message.error('最多只能上传3张图片!');
      return false;
    }

    return true;
  };

  // 上传后触发事件
  const handleChange: UploadProps['onChange'] = ({ fileList: newFileList }) => {
    setFileList(newFileList);
    
    // 获取已上传成功的图片URL
    const uploadedUrls = newFileList
      .filter(file => file.status === 'done')
      .map(file => file.response || file.url)
      .filter(Boolean);
    
    // 回调给父组件
    if (onChange) {
      onChange(uploadedUrls);
    }
  };

  // 预览图片
  const onPreview = async (file: UploadFile) => {
    let src = file.url as string;
    if (!src) {
      src = await new Promise((resolve) => {
        const reader = new FileReader();
        reader.readAsDataURL(file.originFileObj as FileType);
        reader.onload = () => resolve(reader.result as string);
      });
    }
    
    // 在新窗口中打开图片
    const imgWindow = window.open();
    if (imgWindow) {
      imgWindow.document.write(`
        <html>
          <head><title>图片预览</title></head>
          <body style="margin:0;display:flex;justify-content:center;align-items:center;min-height:100vh;background:#000;">
            <img src="${src}" style="max-width:100%;max-height:100%;object-fit:contain;" />
          </body>
        </html>
      `);
    }
  };

  // 上传按钮
  const uploadButton = (
    <div>
      <PlusOutlined />
      <div style={{ marginTop: 8 }}>上传图片</div>
    </div>
  );

  return (
    <Upload
      customRequest={customRequest}
      listType="picture-card"
      fileList={fileList}
      onChange={handleChange}
      onPreview={onPreview}
      beforeUpload={beforeUpload}
      multiple={false}
    >
      {fileList.length >= 3 ? null : uploadButton}
    </Upload>
  );
};

export default DriverLicenseUpload;
