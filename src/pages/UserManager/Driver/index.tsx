import {PlusOutlined} from '@ant-design/icons';
import type {ActionType, ProColumns} from '@ant-design/pro-components';
import {PageContainer, ProTable} from '@ant-design/pro-components';
import '@umijs/max';
import {Button, message, Space, Tag, Typography} from 'antd';
import React, {useEffect, useRef, useState} from 'react';
import {listAllCoreStationVOUsingGet} from "@/services/logistics-backend-api/author/coreStation/coreStationController";
import {listRoleVoUsingGet} from "@/services/logistics-backend-api/author/role/roleController";
import {listAllCoreOrgTreeUsingPost} from "@/services/logistics-backend-api/author/coreOrg/coreOrgController";
import {
  listQueryTruckDriverUsingPost
} from "@/services/logistics-backend-api/base/truckDriver/truckDriverController";
import UpdateForm from "@/pages/UserManager/Driver/components/UpdateForm";

/**
 * 用户管理页面
 *
 * @constructor
 */


const UserAdminPage: React.FC = () => {
  //配置的modal显示
  const [updateModalVisible, handleUpdateModalVisible] = useState<boolean>(false);
  // @ts-ignore TODO
  const actionRef = useRef<ActionType>();
  // 当前用户点击的数据
  const [currentRow, setCurrentRow] = useState<USER_API.UserVO>();
  //岗位
  const [currentCoreStation, setCoreStation] = useState<CORE_STATION_API.CoreStationVO[]>();
  //组织
  const [currentCoreOrg, setCoreOrg] = useState<CORE_ORG_API.CoreOrgTreeVo[]>();
  //角色
  const [currentRole, setRole] = useState<ROLE_API.RoleVO[]>();

  const initCoreStationVO = async () => {
    const res = await listAllCoreStationVOUsingGet();
    if (res.data) {
      setCoreStation(res.data);
    }
  };

  const initCoreOrgTreeVO = async () => {
    const res = await listAllCoreOrgTreeUsingPost();
    if (res.data) {
      setCoreOrg(res.data);
    }
  };

  const initRoleVO = async () => {
    const res = await listRoleVoUsingGet();
    if (res.data) {
      setRole(res.data);
    }
  }

  useEffect(() => {
    initCoreStationVO().then(() => {
    })
    initCoreOrgTreeVO().then(() => {
    })
    initRoleVO().then(() => {
    })
  }, [])
  /**
   * 删除节点
   *
   * @param row
   */
  // const handleDelete = async (row: USER_API.User) => {
  //   console.log(row);
  //   Modal.confirm({
  //     title: '确认删除',
  //     content: `确定要删除用户 "${row.name}" 吗？此操作不可恢复。`,
  //     okText: '确认',
  //     cancelText: '取消',
  //     onOk: async () => {
  //       const hide = message.loading('正在删除');
  //       if (!row) return true;
  //       try {
  //         await deleteUserUsingDelete({
  //           id: row.id as any,
  //         })
  //         hide();
  //         message.success('删除成功');
  //         actionRef?.current?.reload();
  //         return true;
  //       } catch (error: any) {
  //         hide();
  //         message.error('删除失败，' + error.message);
  //         return false;
  //       }
  //     },
  //   })
  //
  // };

  /**
   * 表格列配置
   */
  const columns: ProColumns<TRUCK_DRIVER_API.DriverVO>[] = [
    {
      title: '司机编号',
      dataIndex: 'id',
      valueType: 'text',
      hideInForm: true,
      hideInSearch: true,
    },
    {
      title: '账号',
      dataIndex: 'account',
      valueType: 'text',
      hideInSearch: true,
      hideInForm: true,
      formItemProps: {
        rules: [
          {
            required: true,
            message: '此项为必填项',
          },
        ],
      },
    },
    {
      title: '姓名',
      dataIndex: 'name',
      valueType: 'text',
      hideInForm: true,
      formItemProps: {
        rules: [
          {
            required: true,
            message: '此项为必填项',
          },
        ],
      },
    },
    {
      title: '手机号',
      dataIndex: 'mobile',
      valueType: 'text',
      hideInForm: true,
      hideInSearch: true,
      formItemProps: {
        rules: [
          {
            required: true,
            message: '此项为必填项',
          },
        ],
      },
    },
    {
      title: '所属机构',
      dataIndex: 'orgTreeIds',
      valueType: 'cascader',
      formItemProps: {
        rules: [
          {
            required: true,
            message: '此项为必填项',
          },
        ],
      },
      fieldProps: {
        // 允许选择单层次结构
        changeOnSelect: true,
        options: currentCoreOrg?.map(item => ({
          label: item.name,
          value: item.id,
          children: item.children?.map(childItem => ({
            label: childItem.name,
            value: childItem.id,
            children: childItem.children?.map(childItem => ({
              label: childItem.name,
              value: childItem.id
            })) || []
          })) || []
        })) || []
      },
      render(_, record) {
        if (!record.orgName) {
          return <></>;
        }
        return <Tag color="#5489F5" key={record.orgName}>{record.orgName}</Tag>
      },
    },
    {
      title: '工作状态',
      dataIndex: 'status',
      valueType: 'switch',
      initialValue: 1,
      fieldProps: {
        options: [
          {label: '禁用', value: 0},
          {label: '启用', value: 1},
        ],
      },
      formItemProps: {
        rules: [
          {
            required: true,
            message: '此项为必填项',
          },
        ],
      },
      render: (_, record) => {
        return record.status === 1 ? <Tag color="green">启用</Tag> : <Tag color="red">禁用</Tag>;
      }
    },
    {
      title: '操作',
      dataIndex: 'option',
      valueType: 'option',
      render: (_, record) => (
        <Space size="middle">
          <Typography.Link
            onClick={() => {
              setCurrentRow(record);
              handleUpdateModalVisible(true);

            }}
          >
            配置
          </Typography.Link>

        </Space>
      ),
    },

  ];


  // @ts-ignore
  return (
    <PageContainer>
      <ProTable<USER_API.UserVO>
        headerTitle={'用户表格'}
        actionRef={actionRef}
        rowKey={(record) => record.id || Math.random().toString()}
        search={{
          labelWidth: 120,
        }}
        toolBarRender={() => [
          <Button
            type="primary"
            key="primary"
            onClick={() => {
              // setCreateModalVisible(true);
            }}
          >
            <PlusOutlined/> 新建
          </Button>,
        ]}
        request={async (params, sort, filter) => {
          //拿取排序字段
          const sortField = Object.keys(sort)?.[0];
          //拿取排序方式 ascend/descend
          const sortOrder = sort?.[sortField] ?? undefined;

          try {
            const {data, code} = await listQueryTruckDriverUsingPost({
              ...params,
              //@ts-ignore
              orgId: params.orgTreeIds?.[params.orgTreeIds.length - 1] as number,
              status: Number(params.status),
              sortField,
              sortOrder,
              ...filter,
            } as USER_API.UserQueryRequest);

            return {
              success: code === 0,
              data: data?.records || [],
              total: Number(data?.total) || 0,
            };
          } catch (error: any) {
            message.error('查询失败，' + error.message);
            return {
              success: false,
              data: [],
              total: 0,
            };
          }

        }}
        columns={columns}
      />

      <UpdateForm
        onSubmit={() => {
          handleUpdateModalVisible(false);
          setCurrentRow(undefined);
          actionRef.current.reload();
        }}
        onCancel={() => {
          handleUpdateModalVisible(false);
          setCurrentRow(undefined);
        }}
        updateModalVisible={updateModalVisible}
        values={currentRow || {}}
      />
    </PageContainer>
  );
};
export default UserAdminPage;
