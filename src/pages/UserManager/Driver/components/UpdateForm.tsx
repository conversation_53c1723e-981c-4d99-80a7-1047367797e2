import {
  ProForm,
  ProFormCascader,
  ProFormDatePicker,
  ProFormDateTimePicker,
  ProFormDependency,
  ProFormRadio,
  ProFormSelect,
  ProFormText,
  ProFormTextArea,
  StepsForm,
} from '@ant-design/pro-components';
import {message, Modal} from 'antd';
import React, {useEffect, useState} from 'react';
import {listAllCoreOrgTreeUsingPost} from "@/services/logistics-backend-api/author/coreOrg/coreOrgController";
import {
  editUsingPost,
  getByUserIdUsingGet
} from "@/services/logistics-backend-api/base/truckDriverLicense/truckDriverLicenseController";
import {updateUserUsingPost} from "@/services/logistics-backend-api/author/user/userController";

export type UpdateFormProps = {
  onCancel: () => void;
  onSubmit: (values: TRUCK_DRIVER_API.DriverVO) => void;
  updateModalVisible: boolean;
  values: TRUCK_DRIVER_API.DriverVO;
};

const UpdateForm: React.FC<UpdateFormProps> = (props) => {
  const {values, updateModalVisible, onSubmit, onCancel} = props;
  //根据用户ID去初始化驾驶证详情
  const [truckDriverLicense, setTruckDriverLicense] = useState<TRUCK_DRIVER_LICENSE_API.editUsingPOSTParams>();
  // 存储各步骤的数据
  const [stepOneData, setStepOneData] = useState<TRUCK_DRIVER_API.DriverVO>();
  const [stepTwoData, setStepTwoData] = useState<TRUCK_DRIVER_LICENSE_API.editUsingPOSTParams>();
  const initialTruckDriverLicense = async () => {
    try {
      const {data} = await getByUserIdUsingGet({id: "1024742403489234657"});
      // const {data} = await getByUserIdUsingGet({id: values?.id as number});
      setTruckDriverLicense(data)
    } catch (error: any) {
      message.error(error.message)
    }
  }
  useEffect(() => {
    initialTruckDriverLicense().then(() => {
    })
  }, []);
  return (
    <StepsForm<TRUCK_DRIVER_API.DriverVO>
      stepsProps={{
        size: 'small',
      }}
      stepsFormRender={(dom, submitter) => {
        return (
          <Modal
            width={640}
            styles={{
              body: {
                padding: '32px 40px 48px',
              }
            }}
            destroyOnHidden
            title="规则配置"
            open={props.updateModalVisible}
            footer={submitter}
            onCancel={() => {
              props.onCancel();
            }}
          >
            {dom}
          </Modal>
        );
      }}
      onFinish={async (values: any) => {
        // 这个 onFinish 不会被调用，因为我们在第二步就完成了提交
        console.log("StepsForm onFinish - 这不应该被调用");
        console.log("StepsForm onFinish  values - 这不应该被调用" , values);
        onSubmit?.(values);
        return true;
      }}
    >
      <StepsForm.StepForm<TRUCK_DRIVER_API.DriverVO>
        initialValues={values}
        title="基本信息"
        onFinish={async (values: any) => {
          console.log("第一步表单数据:", values);
          // 保存第一步数据
          setStepOneData(values);
          return true;
        }}

      >
        <ProFormText
          name="id"
          label="员工编号"
          width="md"
          fieldProps={{
            readOnly: true,
            style: {
              backgroundColor: '#f5f5f5',
              color: '#5E5E5E',
            },
          }}
        />
        <ProFormText
          name="name"
          width="md"
          label="司机名称"
          placeholder="请输入至少五个字符"
          fieldProps={{
            readOnly: true,
            style: {
              backgroundColor: '#f5f5f5',
              color: '#5E5E5E',
            },
          }}
        />
        <ProFormCascader
          name="orgTreeIds"
          width="md"
          label="所属机构"
          placeholder="请输入至少五个字符"
          fieldProps={{
            changeOnSelect: true,
          }}
          request={async () => {
            const res = await listAllCoreOrgTreeUsingPost();
            return res?.data?.map(item => ({
              label: item.name,
              value: item.id,
              children: item.children?.map(childItem => ({
                label: childItem.name,
                value: childItem.id,
                children: childItem.children?.map(childItem => ({
                  label: childItem.name,
                  value: childItem.id
                })) || []
              })) || []
            })) || []
          }}
          rules={[
            {
              required: true,
              message: '请选择司机的所属机构',
            }
          ]}
        />
        <ProFormText
          name="mobile"
          label="手机号码"
          width="md"
          placeholder="手机号格式有误"
          rules={[
            {
              required: true,
              message: '请输入手机号',
            },
            {
              pattern: /^1[3456789]\d{9}$/,
              message: '手机号格式有误',
            },
          ]}

        />
      </StepsForm.StepForm>
      <StepsForm.StepForm<TRUCK_DRIVER_LICENSE_API.editUsingPOSTParams>
        initialValues={truckDriverLicense}
        title="驾驶证配信息"
        onFinish={async (values: any) => {
          console.log("第二步表单数据:", values);
          // console.log("选择的驾驶证类型值:", values.licenseType);
          // // 保存第二步数据到状态
          // setStepTwoData(values);
          //
          // // 在第二步完成时，发起两次修改请求
          // try {
          //   // 第一次请求：更新司机基本信息
          //   if (stepOneData) {
          //     console.log("发起第一次请求 - 更新司机信息:", stepOneData);
          //
          //     // 构造用户更新请求数据
          //     const userUpdateData: USER_API.UserUpdateRequest = {
          //       id: stepOneData.id,
          //       orgId: stepOneData.orgTreeIds?.[stepOneData.orgTreeIds.length - 1] as unknown as number,
          //       status: stepOneData.status,
          //     };
          //
          //     const driverUpdateResult = await updateUserUsingPost(userUpdateData);
          //     console.log("司机信息更新结果:", driverUpdateResult);
          //   }
          //
          //   // 第二次请求：更新驾驶证信息
          //   console.log("发起第二次请求 - 更新驾驶证信息:", values);
          //
          //   // 构造驾驶证更新请求数据
          //   const licenseUpdateData: TRUCK_DRIVER_LICENSE_API.editUsingPOSTParams = {
          //     ...values,
          //     userId: stepOneData?.id, // 关联用户ID
          //   };
          //   message.success('信息更新成功！');
          //   // 关闭弹窗并通知父组件
          //   onSubmit?.(stepOneData);
          //
          // } catch (error: any) {
          //   message.error('更新失败：' + error.message);
          //   return false; // 阻止步骤前进
          // }
          onSubmit?.(values);
          return true;
        }}
      >
        <ProForm.Group>
          <ProFormDatePicker
            label="初次领证日期"
            width="sm"
            name="initialCertificateDate"
            rules={[
              {
                required: true,
                message: '请填写初次领证日期',
              },
            ]}
          />
          <ProFormDatePicker
            label="驾驶证有效期"
            width="sm"
            name="validPeriod"
            rules={[
              {
                required: true,
                message: '请填写驾驶证有效期',
              },
            ]}
          />
        </ProForm.Group>
        <ProForm.Group>
          <ProFormText
            name="licenseNumber"
            label="驾驶证号"
            width="sm"
            rules={[
              {
                required: true,
                message: '请填写驾驶证号',
              }
            ]}
          />
          <ProFormDependency name={['licenseType']}>
            {({ licenseType }) => {
              // 根据驾驶证类型映射准驾车型
              const getAllowableType = (type: string) => {
                const typeMap: Record<string, string> = {
                  'A1': '大型客车、A3、B1、B2、C1、C2、C3、C4、M',
                  'A2': '牵引车、B1、B2、C1、C2、C3、C4、M',
                  'A3': '城市公交车、C1、C2、C3、C4',
                  'B1': '中型客车、C1、C2、C3、C4、M',
                  'B2': '大型货车、C1、C2、C3、C4、M',
                  'C1': '小型汽车、C2、C3、C4',
                  'C2': '小型自动挡汽车',
                  'C3': '低速载货汽车、C4',
                  'C4': '三轮汽车',
                  'D': '普通三轮摩托车、E、F',
                  'E': '普通二轮摩托车、F',
                  'F': '轻便摩托车',
                  'M': '轮式自行机械车',
                  'N': '无轨电车',
                  'P': '有轨电车',
                };
                return typeMap[type] || '';
              };

              return (
                <ProFormText
                  name="allowableType"
                  label="准驾车型"
                  width="sm"
                  fieldProps={{
                    readOnly: true,
                    value: getAllowableType(licenseType),
                    style: {
                      backgroundColor: '#f5f5f5',
                      color: '#5E5E5E',
                    },
                  }}
                />
              );
            }}
          </ProFormDependency>

        </ProForm.Group>
        <ProForm.Group>

          <ProFormText
            label="驾龄"
            width="sm"
            name="driverAge"
            fieldProps={{
              readOnly: true,
              style: {
                backgroundColor: '#f5f5f5',
                color: '#5E5E5E',
              },
            }}
          />
          <ProFormSelect
            name="licenseType"
            width="sm"
            label="驾驶证类型"
            valueEnum={{
              'A1': 'A1类型',
              'A2': 'A2类型',
              'A3': 'A3类型',
              'B1': 'B1类型',
              'B2': 'B2类型',
              'C1': 'C1类型',
              'C2': 'C2类型',
              'C3': 'C3类型',
              'C4': 'C4类型',
              'D': 'D类型',
              'E': 'E类型',
              'F': 'F类型',
              'M': 'M类型',
              'N': 'N类型',
              'P': 'P类型',
            }}
            rules={[
              {
                required: true,
                message: '请选择驾驶证类型',
              },
            ]}
          />
        </ProForm.Group>
        <ProForm.Group>
          <ProFormText
            label="从业资格证信息"
            width="sm"
            name="qualificationCertificate"
          />

          <ProFormText
            label="入场证信息"
            width="sm"
            name="passCertificate"
          />
        </ProForm.Group>

      </StepsForm.StepForm>
    </StepsForm>
  );
};

export default UpdateForm;
