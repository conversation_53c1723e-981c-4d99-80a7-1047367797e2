import {
  ProForm,
  ProFormCascader, ProFormDatePicker,
  ProFormDateTimePicker,
  ProFormRadio,
  ProFormSelect,
  ProFormText,
  ProFormTextArea,
  StepsForm,
} from '@ant-design/pro-components';
import {message, Modal} from 'antd';
import React, {useEffect, useState} from 'react';
import {listAllCoreOrgTreeUsingPost} from "@/services/logistics-backend-api/author/coreOrg/coreOrgController";
import {
  getByUserIdUsingGet
} from "@/services/logistics-backend-api/base/truckDriverLicense/truckDriverLicenseController";

export type UpdateFormProps = {
  onCancel: () => void;
  onSubmit: (values: TRUCK_DRIVER_API.DriverVO) => void;
  updateModalVisible: boolean;
  values: TRUCK_DRIVER_API.DriverVO;
};

const UpdateForm: React.FC<UpdateFormProps> = (props) => {
  const { values, updateModalVisible, onSubmit, onCancel } = props;
  //根据用户ID去初始化驾驶证详情
  const [truckDriverLicense, setTruckDriverLicense] = useState<TRUCK_DRIVER_LICENSE_API.editUsingPOSTParams>();
  const initialTruckDriverLicense = async () => {
    try {
      const {data} = await getByUserIdUsingGet({id: "1024742403489234657"});
      // const {data} = await getByUserIdUsingGet({id: values?.id as number});
      setTruckDriverLicense(data)
    }catch (error:any) {
      message.error(error.message)
    }
  }
  useEffect(() => {
    initialTruckDriverLicense().then(()=>{})
  }, []);
  return (
    <StepsForm<TRUCK_DRIVER_API.DriverVO>
      stepsProps={{
        size: 'small',
      }}
      stepsFormRender={(dom, submitter) => {
        return (
          <Modal
            width={640}
            styles={{
              body: {
                padding: '32px 40px 48px',
              }
            }}
            destroyOnHidden
            title="规则配置"
            open={props.updateModalVisible}
            footer={submitter}
            onCancel={() => {
              props.onCancel();
            }}
          >
            {dom}
          </Modal>
        );
      }}
      onFinish={async (values: TRUCK_DRIVER_API.DriverVO) => {
        console.log("DriverVO", values)
        const success = true
        // const success = await handleUpdate({
        //   ...values,
        //   id: oldData.id as any,
        // });
        if (success) {
          onSubmit?.(values);
        }
        return true;
      }}
    >
      <StepsForm.StepForm<TRUCK_DRIVER_API.DriverVO>
        initialValues={values}
        title="基本信息"
      >
        <ProFormText
          name="id"
          label="员工编号"
          width="md"
          fieldProps={{
            readOnly: true,
            style: {
              backgroundColor: '#f5f5f5',
              color: '#5E5E5E',
            },
          }}
        />
        <ProFormText
          name="name"
          width="md"
          label="司机名称"
          placeholder="请输入至少五个字符"
          fieldProps={{
            readOnly: true,
            style: {
              backgroundColor: '#f5f5f5',
              color: '#5E5E5E',
            },
          }}
        />
        <ProFormCascader
          name="orgTreeIds"
          width="md"
          label="所属机构"
          placeholder="请输入至少五个字符"
          fieldProps={{
            changeOnSelect: true,
          }}
          request={async () => {
            const res = await listAllCoreOrgTreeUsingPost();
            return res?.data?.map(item => ({
              label: item.name,
              value: item.id,
              children: item.children?.map(childItem => ({
                label: childItem.name,
                value: childItem.id,
                children: childItem.children?.map(childItem => ({
                  label: childItem.name,
                  value: childItem.id
                })) || []
              })) || []
            })) || []
          }}
          rules={[
            {
              required: true,
              message: '请选择司机的所属机构',
            }
          ]}
        />
        <ProFormText
          name="mobile"
          label="手机号码"
          width="md"
          placeholder="手机号格式有误"
          rules={[
            {
              required: true,
              message: '请输入手机号',
            },
            {
              pattern: /^1[3456789]\d{9}$/,
              message: '手机号格式有误',
            },
          ]}

        />
      </StepsForm.StepForm>
      <StepsForm.StepForm<TRUCK_DRIVER_LICENSE_API.editUsingPOSTParams>
        initialValues={truckDriverLicense}
        title="驾驶证配信息"
      >

        <ProForm.Group title={"123123"}>
          <ProFormText
            name="licenseNumber"
            label="驾驶证号"
            width="md"
            rules={[
              {
                required: true,
                message: '请填写驾驶证号',
              }
            ]}
          />

          <ProFormText
            name="allowableType"
            label="准驾车型"
            width="md"
            fieldProps={{
              readOnly: true,
              style: {
                backgroundColor: '#f5f5f5',
                color: '#5E5E5E',
              },
            }}
          />
          <ProFormDatePicker
            label="初次领证日期"
            width="md"
            name="initialCertificateDate"
            rules={[
              {
                required: true,
                message: '请填写初次领证日期',
              },
            ]}
          />
        </ProForm.Group>

        <ProFormSelect
          name="target"
          width="md"
          label="监控对象"
          valueEnum={{
            0: '表一',
            1: '表二',
          }}
        />
        <ProFormSelect
          name="template"
          width="md"
          label="规则模板"
          valueEnum={{
            0: '规则模板一',
            1: '规则模板二',
          }}
        />
        <ProFormRadio.Group
          name="type"
          label="规则类型"
          options={[
            {
              value: '0',
              label: '强',
            },
            {
              value: '1',
              label: '弱',
            },
          ]}
        />
      </StepsForm.StepForm>
    </StepsForm>
  );
};

export default UpdateForm;
