import {
  ProForm,
  ProFormCascader, ProFormDatePicker,
  ProFormDateTimePicker,
  ProFormRadio,
  ProFormSelect,
  ProFormText,
  ProFormTextArea,
  StepsForm,
} from '@ant-design/pro-components';
import {message, Modal} from 'antd';
import React, {useEffect, useState} from 'react';
import {listAllCoreOrgTreeUsingPost} from "@/services/logistics-backend-api/author/coreOrg/coreOrgController";
import {
  getByUserIdUsingGet
} from "@/services/logistics-backend-api/base/truckDriverLicense/truckDriverLicenseController";

export type UpdateFormProps = {
  onCancel: () => void;
  onSubmit: (values: TRUCK_DRIVER_API.DriverVO) => void;
  updateModalVisible: boolean;
  values: TRUCK_DRIVER_API.DriverVO;
};

const UpdateForm: React.FC<UpdateFormProps> = (props) => {
  const {values, updateModalVisible, onSubmit, onCancel} = props;
  //根据用户ID去初始化驾驶证详情
  const [truckDriverLicense, setTruckDriverLicense] = useState<TRUCK_DRIVER_LICENSE_API.editUsingPOSTParams>();
  const initialTruckDriverLicense = async () => {
    try {
      const {data} = await getByUserIdUsingGet({id: "1024742403489234657"});
      // const {data} = await getByUserIdUsingGet({id: values?.id as number});
      setTruckDriverLicense(data)
    } catch (error: any) {
      message.error(error.message)
    }
  }
  useEffect(() => {
    initialTruckDriverLicense().then(() => {
    })
  }, []);
  return (
    <StepsForm<TRUCK_DRIVER_API.DriverVO>
      stepsProps={{
        size: 'small',
      }}
      stepsFormRender={(dom, submitter) => {
        return (
          <Modal
            width={640}
            styles={{
              body: {
                padding: '32px 40px 48px',
              }
            }}
            destroyOnHidden
            title="规则配置"
            open={props.updateModalVisible}
            footer={submitter}
            onCancel={() => {
              props.onCancel();
            }}
          >
            {dom}
          </Modal>
        );
      }}
      onFinish={async (values: any) => {
        console.log("values:", values);
        const newTruckDriver: TRUCK_DRIVER_API.DriverVO = {
          ...values
        }

        const newTruckDriverLicense: TRUCK_DRIVER_LICENSE_API.editUsingPOSTParams = {
          ...values
        }
        console.log("newTruckDriver:", newTruckDriver);
        console.log("newTruckDriverLicense:", newTruckDriverLicense);

        const success = true
        // const success = await handleUpdate(allData);
        if (success) {
          onSubmit?.(values);
        }
        return true;
      }}
    >
      <StepsForm.StepForm<TRUCK_DRIVER_API.DriverVO>
        initialValues={values}
        title="基本信息"
        onFinish={async (values) => {
          console.log("第一步")
          return true;
        }}

      >
        <ProFormText
          name="id"
          label="员工编号"
          width="md"
          fieldProps={{
            readOnly: true,
            style: {
              backgroundColor: '#f5f5f5',
              color: '#5E5E5E',
            },
          }}
        />
        <ProFormText
          name="name"
          width="md"
          label="司机名称"
          placeholder="请输入至少五个字符"
          fieldProps={{
            readOnly: true,
            style: {
              backgroundColor: '#f5f5f5',
              color: '#5E5E5E',
            },
          }}
        />
        <ProFormCascader
          name="orgTreeIds"
          width="md"
          label="所属机构"
          placeholder="请输入至少五个字符"
          fieldProps={{
            changeOnSelect: true,
          }}
          request={async () => {
            const res = await listAllCoreOrgTreeUsingPost();
            return res?.data?.map(item => ({
              label: item.name,
              value: item.id,
              children: item.children?.map(childItem => ({
                label: childItem.name,
                value: childItem.id,
                children: childItem.children?.map(childItem => ({
                  label: childItem.name,
                  value: childItem.id
                })) || []
              })) || []
            })) || []
          }}
          rules={[
            {
              required: true,
              message: '请选择司机的所属机构',
            }
          ]}
        />
        <ProFormText
          name="mobile"
          label="手机号码"
          width="md"
          placeholder="手机号格式有误"
          rules={[
            {
              required: true,
              message: '请输入手机号',
            },
            {
              pattern: /^1[3456789]\d{9}$/,
              message: '手机号格式有误',
            },
          ]}

        />
      </StepsForm.StepForm>
      <StepsForm.StepForm<TRUCK_DRIVER_LICENSE_API.editUsingPOSTParams>
        initialValues={truckDriverLicense}
        title="驾驶证配信息"
        onFinish={async (values) => {
          console.log("第二步表单数据:", values);
          console.log("选择的驾驶证类型值:", values.licenseType);
          // 保存第二步数据到状态
          setTruckDriverLicense(values);
          return true;
        }}
      >
        <ProForm.Group>
          <ProFormDatePicker
            label="初次领证日期"
            width="sm"
            name="initialCertificateDate"
            rules={[
              {
                required: true,
                message: '请填写初次领证日期',
              },
            ]}
          />
          <ProFormDatePicker
            label="驾驶证有效期"
            width="sm"
            name="validPeriod"
            rules={[
              {
                required: true,
                message: '请填写驾驶证有效期',
              },
            ]}
          />
        </ProForm.Group>
        <ProForm.Group>
          <ProFormText
            name="licenseNumber"
            label="驾驶证号"
            width="sm"
            rules={[
              {
                required: true,
                message: '请填写驾驶证号',
              }
            ]}
          />
          <ProFormDependency name={['licenseType']}>
            {({ licenseType }) => {
              // 根据驾驶证类型映射准驾车型
              const getAllowableType = (type: string) => {
                const typeMap: Record<string, string> = {
                  'A1': '大型客车、A3、B1、B2、C1、C2、C3、C4、M',
                  'A2': '牵引车、B1、B2、C1、C2、C3、C4、M',
                  'A3': '城市公交车、C1、C2、C3、C4',
                  'B1': '中型客车、C1、C2、C3、C4、M',
                  'B2': '大型货车、C1、C2、C3、C4、M',
                  'C1': '小型汽车、C2、C3、C4',
                  'C2': '小型自动挡汽车',
                  'C3': '低速载货汽车、C4',
                  'C4': '三轮汽车',
                  'D': '普通三轮摩托车、E、F',
                  'E': '普通二轮摩托车、F',
                  'F': '轻便摩托车',
                  'M': '轮式自行机械车',
                  'N': '无轨电车',
                  'P': '有轨电车',
                };
                return typeMap[type] || '';
              };

              return (
                <ProFormText
                  name="allowableType"
                  label="准驾车型"
                  width="sm"
                  fieldProps={{
                    readOnly: true,
                    value: getAllowableType(licenseType),
                    style: {
                      backgroundColor: '#f5f5f5',
                      color: '#5E5E5E',
                    },
                  }}
                />
              );
            }}
          </ProFormDependency>

        </ProForm.Group>
        <ProForm.Group>

          <ProFormText
            label="驾龄"
            width="sm"
            name="driverAge"
            fieldProps={{
              readOnly: true,
              style: {
                backgroundColor: '#f5f5f5',
                color: '#5E5E5E',
              },
            }}
          />
          <ProFormSelect
            name="licenseType"
            width="sm"
            label="驾驶证类型"
            valueEnum={{
              'A1': 'A1类型(大型客车)',
              'A2': 'A2类型(牵引车)',
              'A3': 'A3类型(城市公交车)',
              'B1': 'B1类型(中型客车)',
              'B2': 'B2类型(大型货车)',
              'C1': 'C1类型(小型汽车)',
              'C2': 'C2类型(小型自动挡汽车)',
              'C3': 'C3类型(低速载货汽车)',
              'C4': 'C4类型(三轮汽车)',
              'D': 'D类型(普通三轮摩托车)',
              'E': 'E类型(普通二轮摩托车)',
              'F': 'F类型(轻便摩托车)',
              'M': 'M类型(轮式自行机械车)',
              'N': 'N类型(无轨电车)',
              'P': 'P类型(有轨电车)',
            }}
            rules={[
              {
                required: true,
                message: '请选择驾驶证类型',
              },
            ]}
          />
        </ProForm.Group>
        <ProForm.Group>
          <ProFormText
            label="从业资格证信息"
            width="sm"
            name="qualificationCertificate"
          />

          <ProFormText
            label="入场证信息"
            width="sm"
            name="passCertificate"
          />
        </ProForm.Group>

        {/*<ProFormSelect*/}
        {/*  name="target"*/}
        {/*  width="md"*/}
        {/*  label="监控对象"*/}
        {/*  valueEnum={{*/}
        {/*    0: '表一',*/}
        {/*    1: '表二',*/}
        {/*  }}*/}
        {/*/>*/}

      </StepsForm.StepForm>
    </StepsForm>
  );
};

export default UpdateForm;
