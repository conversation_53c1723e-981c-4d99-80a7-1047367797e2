import {type ActionType, PageContainer, ProColumns, ProTable} from "@ant-design/pro-components";
import React, {useRef, useState} from "react";
import {But<PERSON>} from "antd";
import {PlusOutlined} from "@ant-design/icons";
import {listRoleUsingGet} from "@/services/logistics-backend-api/role/roleController";
import CreateModal from "@/pages/Admin/Role/components/CreateModal";


const RoleAdminPage: React.FC = () => {
  // 是否显示新建窗口
  const [createModalVisible, setCreateModalVisible] = useState<boolean>(false);

  //当前用户点击的数据
  const [currentRow, setCurrentRow] = useState<ROLE_API.Role>();


  // @ts-ignore TODO
  const actionRef = useRef<ActionType>();
  const columns: ProColumns<ROLE_API.Role>[] = [

  ]
  return (
    <PageContainer>
      <ProTable<ROLE_API.Role>
        headerTitle={'角色表格'}
        rowKey="id"
        search={{
          labelWidth: 120,
        }}
        toolBarRender={() => [
          <Button type="primary" key="primary" icon={<PlusOutlined/>} onClick={() => {
            setCreateModalVisible(true)
          }}>
            新建
          </Button>
        ]}
        request={async (params, sort, filter) => {
          //拿取排序字段
          const sortField = Object.keys(sort)?.[0];
          //拿取排序方式 ascend/descend
          const sortOrder = sort?.[sortField] ?? undefined;

          // const {code,data} =  await listRoleUsingGet({
          //   ...params,
          //   sortField,
          //   sortOrder,
          //   ...filter,
          // } as ROLE_API.RoleQueryRequest)

          return {
            success: true,
            data: [],
            total: 0,
          };
        }
        }
      ></ProTable>
      <CreateModal visible={createModalVisible} columns={columns} onSubmit={() =>{
        // 关闭弹窗
        setCreateModalVisible(false);
        //将数据清空，不然下次打开还是上次添加的数据
        setCurrentRow(undefined);
        // reload后会调用request方法
        actionRef.current?.reload();
      }} onCancel={()=>{
        //关闭弹窗函数
        setCreateModalVisible(false)}
      }></CreateModal>
    </PageContainer>
  )
};
export default RoleAdminPage;
