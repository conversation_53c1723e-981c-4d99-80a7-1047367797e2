import {ProColumns, ProTable} from '@ant-design/pro-components';
import '@umijs/max';
import {message, Modal} from 'antd';
import React from 'react';
import {updateRoleUsingDelete} from "@/services/logistics-backend-api/role/roleController";

interface Props {
  oldData?: ROLE_API.Role;
  visible: boolean;
  columns: ProColumns<ROLE_API.Role>[];
  onSubmit: (values: ROLE_API.RoleAddRequest) => void;
  onCancel: () => void;
}


/**
 * 更新节点
 *
 * @param fields
 */
const handleUpdate = async (fields: ROLE_API.RoleUpdateRequest) => {

  const hide = message.loading('正在更新');
  try {
    await updateRoleUsingDelete(fields);
    hide();
    message.success('更新成功');
    return true;
  } catch (error: any) {
    hide();
    message.error('更新失败，' + error.message);
    return false;
  }
};

/**
 * 更新弹窗
 * @param props
 * @constructor
 */
const UpdateModal: React.FC<Props> = (props) => {
  // oldData:需要更改的元数据
  // visible:是否显示弹窗
  // columns:表单列表
  // onSubmit:提交表单
  // onCancel ：取消表单显示

  const {oldData, visible, columns, onSubmit, onCancel} = props;

  //可以不需要useMemo这就意味着columns.map这些操作会很频繁
  const updateColumns = React.useMemo(() => {
    return columns.map(col => {
      // 在更新表单中隐藏这些字段
      if (col.dataIndex === 'account' || col.dataIndex === 'roleName') {
        return {...col, hideInForm: true};
      }
      return {...col};
    });
  }, [columns]);
  if (!oldData) {
    return <></>;
  }

  return (
    <Modal
      destroyOnClose
      title={'更新'}
      open={visible}
      footer={null}
      onCancel={() => {
        onCancel?.();
      }}
    >
      <ProTable
        type="form"
        columns={updateColumns}
        form={{
          initialValues: oldData,
        }}
        onSubmit={async (values: USER_API.UserUpdateRequest) => {
          const success = await handleUpdate({
              ...values,
            id: oldData.id as any,
          });
          if (success) {
            onSubmit?.(values);
          }
        }}
      />
    </Modal>
  );
};
export default UpdateModal;
