import {ProColumns, ProTable} from '@ant-design/pro-components';
import '@umijs/max';
import {message, Modal} from 'antd';
import React from 'react';
import {addUserUsingPost} from "@/services/logistics-backend-api/user/userController";
import {DEFAULT_AVATAR} from "@/constants";

interface Props {
  visible: boolean;
  columns: ProColumns<USER_API.User>[];
  onSubmit: (values: USER_API.UserAddRequest) => void;
  onCancel: () => void;
}

/**
 * 添加节点
 * @param fields
 */
const handleAdd = async (fields: USER_API.UserAddRequest) => {
  //@ts-ignore
  fields = {
    ...fields,
    //@ts-ignore
    orgId: fields.orgTreeIds?.[fields.orgTreeIds.length - 1] as unknown as number,
    status: Number(fields.status),
    avatar: DEFAULT_AVATAR,
  }
  console.log("fields", fields)
  const hide = message.loading('正在添加');
  try {
    await addUserUsingPost(fields);
    hide();
    message.success('创建成功');
    return true;
  } catch (error: any) {
    hide();
    message.error('创建失败，' + error.message);
    return false;
  }
};

/**
 * 创建弹窗
 * @param props
 * @constructor
 */
const CreateModal: React.FC<Props> = (props) => {
  const {visible, columns, onSubmit, onCancel} = props;

  //可以不需要useMemo这就意味着columns.map这些操作会很频繁
  const createColumns = React.useMemo(() => {
    return columns.map(col => {
      // 在创建表单中隐藏这些字段
      if (col.dataIndex === 'account' || col.dataIndex === 'orgName'||
        col.dataIndex === 'status' || col.dataIndex === 'stationName' ||
        col.dataIndex === 'mobile' || col.dataIndex === 'name' || col.dataIndex === 'sex'
    )
      {
        return {...col, hideInForm: false}
      }
      return {...col}
    });
  }, [columns]);


  return (
    <Modal
      destroyOnClose
      title={'创建'}
      open={visible}
      footer={null}
      onCancel={() => {
        onCancel?.();
      }}
    >
      <ProTable
        type="form"
        columns={createColumns}
        onSubmit={async (values: USER_API.UserAddRequest) => {
          const success = await handleAdd(values);
          if (success) {
            onSubmit?.(values);
          }
        }}
      />
    </Modal>
  );
};
export default CreateModal;
