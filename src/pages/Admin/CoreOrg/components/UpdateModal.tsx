// import {ProColumns, ProTable} from '@ant-design/pro-components';
// import '@umijs/max';
// import {message, Modal} from 'antd';
// import React from 'react';
// import {updateCoreOrgUsingPost} from "@/services/logistics-backend-api/coreOrg/coreOrgController";
//
// interface Props {
//   oldData?: CORE_ORG_API.CoreOrgTreeVo;
//   visible: boolean;
//   columns: ProColumns<CORE_ORG_API.CoreOrgTreeVo>[];
//   onSubmit: (values: CORE_ORG_API.CoreOrgAddRequest) => void;
//   onCancel: () => void;
// }
//
//
// /**
//  * 更新节点
//  *
//  * @param fields
//  */
// const handleUpdate = async (fields: CORE_ORG_API.CoreOrgUpdateRequest) => {
//   const hide = message.loading('正在更新');
//   console.log("fields", fields)
//   try {
//     await updateCoreOrgUsingPost({
//       ...fields,
//       orgType: Number(fields.orgType),
//       status: Number(fields.status),
//     });
//     hide();
//     message.success('更新成功');
//     return true;
//   } catch (error: any) {
//     hide();
//     message.error('更新失败，' + error.message);
//     return false;
//   }
// };
//
// /**
//  * 更新弹窗
//  * @param props
//  * @constructor
//  */
// const RoleUpdateModal: React.FC<Props> = (props) => {
//   const {oldData, visible, columns, onSubmit, onCancel} = props;
//   return (
//     <Modal
//       destroyOnClose
//       title={'更新'}
//       open={visible}
//       footer={null}
//       onCancel={() => {
//         onCancel?.();
//       }}
//     >
//       <ProTable
//         type="form"
//         columns={columns}
//         form={{
//           initialValues: oldData,
//         }}
//         onSubmit={async (values: CORE_ORG_API.CoreOrgUpdateRequest) => {
//           const success = await handleUpdate({
//             ...values,
//             id: oldData?.id as any,
//           });
//           if (success) {
//             onSubmit?.(values);
//           }
//         }}
//       />
//     </Modal>
//   );
// };
//
// export default RoleUpdateModal;
