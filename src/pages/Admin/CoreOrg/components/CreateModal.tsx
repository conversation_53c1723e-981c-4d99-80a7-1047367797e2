import {ProColumns, ProTable} from '@ant-design/pro-components';
import '@umijs/max';
import {message, Modal} from 'antd';
import React from 'react';
import {addRoleUsingPost} from "@/services/logistics-backend-api/role/roleController";

interface Props {
  visible: boolean;
  columns: ProColumns<CORE_ORG_API.CoreOrgTreeVo>[];
  onSubmit: (values: CORE_ORG_API.CoreOrgAddRequest) => void;
  onCancel: () => void;
}

/**
 * 添加节点
 * @param fields
 */
const handleAdd = async (fields: CORE_ORG_API.CoreOrgAddRequest) => {
  const hide = message.loading('正在添加');
  try {
    await addRoleUsingPost(fields);
    hide();
    message.success('创建成功');
    return true;
  } catch (error: any) {
    hide();
    message.error('创建失败，' + error.message);
    return false;
  }
};

/**
 * 创建弹窗
 * @param props
 * @constructor
 */
const CreateModal: React.FC<Props> = (props) => {
  const {visible, columns, onSubmit, onCancel} = props;
  return (
    <Modal
      destroyOnHidden
      title={'添加角色'}
      open={visible}
      footer={null}
      onCancel={() => {
        onCancel?.();
      }}
    >
      <ProTable
        type="form"
        columns={columns}
        onSubmit={async (values: CORE_ORG_API.CoreOrgAddRequest) => {
          const success = await handleAdd({
            ...values,
            status: Number(values.status),
          });
          //成功后触发回调让父组件reload
          if (success) {
            onSubmit?.(values);
          }
        }}
      />
    </Modal>
  );
};
export default CreateModal;
