import {PageContainer, ProCard, type ProColumns, ProTable} from '@ant-design/pro-components';
import '@umijs/max';
import React, {useEffect, useRef, useState} from 'react';
import {Badge, Button, Space, Tag, Typography} from "antd";
import {PlusOutlined} from "@ant-design/icons";
import CreateModal from "@/pages/Admin/Role/components/CreateModal";
import UpdateModal from "@/pages/Admin/Role/components/UpdateModal";
import {
  listAllCoreOrgTreeUsingPost,
  listQueryAllCoreOrgTreeUsingPost
} from "@/services/logistics-backend-api/coreOrg/coreOrgController";
import {listRoleUsingGet} from "@/services/logistics-backend-api/role/roleController";

/**
 * 用户管理页面
 *
 * @constructor
 */
type coreOrgTreeVo = {
  id?: number;
  name?: string;
  parentId?: number;
  /**
   ＊部门类型1为分公司，2为一级转运中心、3为二级转运中心、4为网点
   */
  orgType?: number;
  children?: coreOrgTreeVo[];
};
const UserAdminPage: React.FC = () => {
    // 是否显示新建窗口
    const [createModalVisible, setCreateModalVisible] = useState<boolean>(false);
    // @ts-ignore TODO
    const actionRef = useRef<ActionType>();
    // 是否显示新建窗口
    const [updateModalVisible, setUpdateModalVisible] = useState<boolean>(false);
    //当前用户点击的数据
    const [currentRow, setCurrentRow] = useState<ROLE_API.Role>();

  //组织
  const [currentCoreOrg, setCoreOrg] = useState<CORE_ORG_API.CoreOrgTreeVo[]>();

  const initCoreOrgTreeVO = async () => {
    const res = await listAllCoreOrgTreeUsingPost();
    if (res.data) {
      setCoreOrg(res.data);
    }
  };

  useEffect(() => {
    initCoreOrgTreeVO().then(() => {
    })
  }, [])
    const columns: ProColumns<CORE_ORG_API.CoreOrgTreeVo>[] = [
      {
        title: 'id',
        dataIndex: 'id',
        valueType: 'text',
        hideInForm: true,
        hideInSearch: true,
        hideInTable: true,
      },
      {
        title: '组织名称',
        dataIndex: 'name',
        valueType: 'text',
        formItemProps: {
          rules: [
            {
              required: true,
              message: '此项为必填项',
            },
          ],
        },
        render(_, record) {
          if (!record.name) {
            return <></>;
          }
          return <Tag color="#50A14F" key={record.name}>{record.name}</Tag>
        },
      },
      {
        title: '组织类型',
        dataIndex: 'orgType',
        valueType: 'select',
        valueEnum: {
          1: {text: '分公司', status: 'Success'},
          2: {text: '一级转运中心', status: 'Warning'},
          3: {text: '二级转运中心', status: 'Error'},
          4: {text: '网点', status: 'Default'},
        },
        formItemProps: {
          rules: [
            {
              required: true,
              message: '此项为必填项',
            },
          ],
        },
      },
      {
        title: '上级组织',
        dataIndex: 'parentId',
        valueType: 'treeSelect',
        hideInTable: true,
        formItemProps: {
          rules: [
            {
              required: true,
              message: '此项为必填项',
            },
          ],
        },
        fieldProps: {
          // 允许选择单层次结构
          changeOnSelect: true,
          options: [
            { label: '顶级组织', value: -1 },
            ...currentCoreOrg?.map(item => ({
              value: item.id,
              label: item.name,
              children: item.children?.map(childItem => ({
                value: childItem.id,
                label: childItem.name,
                children: childItem.children?.map(grandChildItem => ({
                  value: grandChildItem.id,
                  label: grandChildItem.name,
                })) || []
              })) || []
            })) || []
          ]
        },
        render(_, record) {
          if (!record.parentName) {
            return <></>;
          }
          return <Tag color="#358AB8" key={record.parentName}>{record.parentName}</Tag>
        },
      },
      {
        title: '状态',
        dataIndex: 'status',
        valueType: 'switch',
        initialValue: 1,
        fieldProps: {
          options: [
            {label: '禁用', value: 0},
            {label: '启用', value: 1},
          ],
        },
        render: (_, record) => {
          return 1 === 1 ? <Badge status="success" text="启用"/> : <Badge status="error" text="禁用"/>;
        }
      },
      {
        title: '操作',
        dataIndex: 'option',
        valueType: 'option',
        render: (_, record) => [
          <Space size="middle" key={record?.id}>
            <Typography.Link
              onClick={() => {
                setCurrentRow(record);
                setUpdateModalVisible(true);
              }}
            >
              修改
            </Typography.Link>
            <Typography.Link type="danger" onClick={() => {
            }}>
              删除
            </Typography.Link>
          </Space>
        ],
      }
    ]

    return (
      <PageContainer>
        <ProTable<CORE_ORG_API.CoreOrgTreeVo>
          headerTitle={'角色表格'}
          actionRef={actionRef}
          rowKey={(record) => record.id || Math.random().toString()}
          search={{labelWidth: 120,}}
          toolBarRender={() => [
            <Button type="primary" key="primary" onClick={() => {
              setCreateModalVisible(true)
            }}>
              <PlusOutlined/> 新建
            </Button>
          ]}
          request={async (params, sort, filter) => {
            const sortField = Object.keys(sort)?.[0];
            const sortOrder = sort?.[sortField] ?? undefined;

            const {code, data} = await listQueryAllCoreOrgTreeUsingPost({
              ...params,
              status: Number(params.status),
              sortField,
              sortOrder,
              ...filter,
            });

            // data?.forEach(item => {
            //   item.parentId = undefined;
            // });
            return {
              /**
               * 当你在 ProTable 中返回树形结构的数据时，它会自动帮你调整样式并展示为树形结构。
               * 工作原理
               * 1.数据结构识别：ProTable 会自动识别具有 children 属性的嵌套数据结构
               * 2.自动树形展示：当检测到这种结构时，会自动渲染为可展开/折叠的树形表格
               * 3.展开控制：通过 expandable 属性可以控制展开行为
               */
              success: code === 0,
              data: data || [],
            };
          }}
          columns={columns}
          pagination={false}
          expandable={{
            childrenColumnName: 'children',
          }}
        />
        <CreateModal
          visible={createModalVisible}
          columns={columns}
          onSubmit={() => {
            // 关闭弹窗
            setCreateModalVisible(false);
            //将数据清空，不然下次打开还是上次添加的数据
            setCurrentRow(undefined);
            // reload后会调用request方法
            actionRef.current?.reload();
          }} onCancel={() => {
          //关闭弹窗函数
          setCreateModalVisible(false)
        }
        }/>

        <UpdateModal
          visible={updateModalVisible}
          columns={columns}
          oldData={currentRow}
          onSubmit={() => {
            setUpdateModalVisible(false);
            setCurrentRow(undefined);
            // reload后会调用request方法
            actionRef.current?.reload();
          }}
          onCancel={() => {
            setUpdateModalVisible(false);
          }}
        />
      </PageContainer>
    );
  }
;
export default UserAdminPage;
