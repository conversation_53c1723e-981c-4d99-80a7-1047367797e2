export default [
  {
    path: '/user',
    layout: false,
    routes: [
      {name: '登录', path: '/user/login', component: './user/login'},
      {name: '注册', path: '/user/register', component: './user/register'}

    ],
  },
  {
    path: '/admin',
    icon: 'crown',
    name: "管理页",
    access: 'canAdmin',
    routes: [
      { path: '/admin', redirect: '/admin/user' },
      { icon: 'table', path: '/admin/user', component: './Admin/User', name: "用户管理" },
      { icon: 'tools', path: '/admin/generator', component: './Admin/Generator', name: "生成器管理" },
    ],
  },
  {path: '/', redirect: '/welcome'},
  {path: '/welcome',component: './welcome.tsx'},
  {path: '*', layout: false, component: './404'},
];
